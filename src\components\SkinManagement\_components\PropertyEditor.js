'use client';
import { useDispatch } from 'react-redux';
import { updateCanvasItem } from '@/store/features/canvasSlice';
import { format } from 'date-fns';
import {
  fontFamilies,
  fontSizes,
  colors,
  dateFormats,
  textAlignments,
} from '@/store/features/canvasSlice';
import { Icon } from '@iconify/react';

const PropertyEditor = ({ selectedItem }) => {
  if (!selectedItem) return null;

  return (
    <div className="mt-6 p-3 bg-white rounded-md border border-[#E6D7A9] shadow-sm">
      {selectedItem.type != 'image' && (
        <TextPropertyEditor selectedItem={selectedItem} />
      )}
    </div>
  );
};

const TextPropertyEditor = ({ selectedItem }) => {
  const dispatch = useDispatch();

  const handleUpdateItem = (id, updates) => {
    dispatch(updateCanvasItem({ id, updates }));
  };

  // Use the hex color directly in inline styles
  const handleColorChange = (hexColor) => {
    handleUpdateItem(selectedItem.id, {
      styles: {
        ...selectedItem.styles,
        color: hexColor, // Store the hex color directly in styles
      },
    });
  };
  return (
    <div className="space-y-4">
      {/* Date format selector - only for date type */}
      {selectedItem.textType === 'date' && (
        <div className="mb-4">
          <label className="block text-sm font-medium mb-1 text-[#723F11]">
            Date Format
          </label>
          <select
            value={selectedItem.dateFormat || 'YYYY-MM-DD'}
            onChange={(e) => {
              const newFormat = e.target.value;
              handleUpdateItem(selectedItem.id, {
                dateFormat: newFormat,
                content: format(new Date(), newFormat),
              });
            }}
            className="w-full p-2 border border-gray-300 rounded-md text-sm"
          >
            {dateFormats.map((df, index) => (
              <option key={index} value={df.value}>
                {df.name}
              </option>
            ))}
          </select>
        </div>
      )}

      {/* Font family selector */}
      <div className="mb-4">
        <label className="block text-sm font-medium mb-1 text-[#723F11]">
          Font Family
        </label>
        <select
          value={selectedItem.styles?.fontFamily || 'Roboto'}
          onChange={(e) =>
            handleUpdateItem(selectedItem.id, {
              styles: {
                ...selectedItem.styles,
                fontFamily: e.target.value,
              },
            })
          }
          className="w-full p-2 border border-gray-300 rounded-md text-sm"
        >
          {fontFamilies.map((font, index) => (
            <option key={index} value={font.value}>
              {font.name}
            </option>
          ))}
        </select>
      </div>

      {/* Font size selector */}
      <div className="mb-4">
        <label className="block text-sm font-medium mb-1 text-[#723F11]">
          Font Size
        </label>
        <select
          value={selectedItem.styles?.fontSize || '1rem'}
          onChange={(e) =>
            handleUpdateItem(selectedItem.id, {
              styles: {
                ...selectedItem.styles,
                fontSize: e.target.value,
              },
            })
          }
          className="w-full p-2 border border-gray-300 rounded-md text-sm"
        >
          {fontSizes.map((size, index) => (
            <option key={index} value={size.value}>
              {size.name}
            </option>
          ))}
        </select>
      </div>

      {/* Text alignment selector - show for all text types */}
      <div className="mb-4 border-b pb-4 border-[#E6D7A9]">
        <label className="block text-sm font-medium mb-1 text-[#723F11]">
          Text Alignment
        </label>
        <div className="flex border border-gray-300 rounded-md overflow-hidden">
          {textAlignments.map((align, index) => (
            <button
              key={index}
              onClick={() =>
                handleUpdateItem(selectedItem.id, {
                  styles: {
                    ...selectedItem.styles,
                    textAlign: align.value,
                  },
                })
              }
              className={`flex-1 py-2 px-3 flex items-center justify-center ${
                selectedItem.styles?.textAlign === align.value
                  ? 'bg-yellow-100 text-yellow-800'
                  : 'bg-white text-gray-700 hover:bg-gray-50'
              }`}
              title={align.name}
            >
              <Icon icon={align.icon} className="w-5 h-5" />
            </button>
          ))}
        </div>
      </div>

      {/* Text color selector */}
      <div className="mb-4">
        <label className="block text-sm font-medium mb-1 text-[#723F11]">
          Text Color
        </label>
        <div className="grid grid-cols-6 gap-2">
          {colors.map((color, index) => {
            const hexColor = color.value;

            return (
              <button
                key={index}
                onClick={() =>
                  handleUpdateItem(selectedItem.id, {
                    styles: {
                      ...selectedItem.styles,
                      color: hexColor,
                    },
                  })
                }
                className={`p-0 rounded-sm border ${
                  selectedItem.styles?.color === hexColor
                    ? 'border-yellow-500 ring-1 ring-yellow-400'
                    : 'border-gray-300'
                }`}
                title={color.name}
              >
                <div
                  className="w-full h-5 rounded-sm"
                  style={{ backgroundColor: hexColor }}
                ></div>
              </button>
            );
          })}

          {/* Custom color picker button with input type color */}
          <div className="p-0 rounded-sm border border-gray-300 overflow-hidden relative">
            <input
              type="color"
              onChange={(e) => handleColorChange(e.target.value)}
              className="absolute inset-0 opacity-0 cursor-pointer w-full h-full"
              title="Choose Custom Color"
            />
            <div className="w-full h-5 bg-white flex items-center justify-center text-xs font-medium">
              <span className="text-gray-600">+</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default PropertyEditor;
