import React from 'react';
import { Icon } from '@iconify/react';
import { motion, AnimatePresence } from 'framer-motion';

const AnswerCheckModal = ({ 
  isOpen, 
  currentQuestion, 
  userAnswers, 
  isCorrect, 
  onPlayAgain, 
  onNextChallenge,
  currentQuestionIndex,
  totalQuestions
}) => {
  if (!isOpen || !currentQuestion) return null;

  // Generate the sentence with highlighted answers
  const generateHighlightedSentence = () => {
    let gapIndex = 0;
    return currentQuestion.question_text_plain.replace(
      /\[\[gap\]\]/g,
      () => {
        const userAnswer = userAnswers[gapIndex] || '';
        const correctAnswer = currentQuestion.correct_answers[gapIndex] || '';
        const isAnswerCorrect = userAnswer === correctAnswer;

        gapIndex++;

        if (isAnswerCorrect) {
          // Show only correct answer with green background
          return `<span class="inline-block mx-1 px-1 rounded-lg font-semibold bg-green-100 border-green-300 text-green-800 border-2">${correctAnswer}</span>`;
        } else {
          // Show both user's wrong answer (with line-through) and correct answer
          return `<span class="inline-block mx-1"><span class="px-1 rounded-lg font-semibold bg-red-100 border-red-300 text-red-800 border-2 line-through">${userAnswer}</span> <span class="px-1 rounded-lg font-semibold bg-green-100 border-green-300 text-green-800 border-2">${correctAnswer}</span></span>`;
        }
      }
    );
  };

  const isLastQuestion = currentQuestionIndex === totalQuestions - 1;

  return (
    <AnimatePresence>
      {isOpen && (
        <div className="fixed inset-0 z-50 flex items-center justify-center p-4">
          {/* Backdrop */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="absolute inset-0 bg-black/50 backdrop-blur-sm"
          />

          {/* Modal */}
          <motion.div
            initial={{ opacity: 0, scale: 0.8, y: 20 }}
            animate={{ opacity: 1, scale: 1, y: 0 }}
            exit={{ opacity: 0, scale: 0.8, y: 20 }}
            transition={{ type: "spring", duration: 0.4 }}
            className="relative bg-gradient-to-br from-yellow-50 via-orange-50 to-yellow-100 rounded-2xl shadow-xl max-w-xl w-full border-2 border-yellow-400 max-h-[90vh] overflow-y-auto"
          >
            {/* Header    
            className="relative bg-gradient-to-br from-yellow-50 via-orange-50 to-yellow-100 rounded-2xl shadow-xl max-w-sm w-full border-2 border-yellow-400"*/}
            <div className={`p-6 text-white text-center rounded-t-2xl relative overflow-hidden bg-gradient-to-br from-yellow-400 to-yellow-300 `}>
              {/* Magical shimmer effect */}
              <motion.div
                className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent"
                animate={{ x: [-100, 300] }}
                transition={{ duration: 3, repeat: Infinity, repeatDelay: 2 }}
              />

              <motion.div
                initial={{ scale: 0 }}
                animate={{ scale: 1 }}
                transition={{ delay: 0.1, type: "spring" }}
                className="w-16 h-16 bg-white/20 rounded-full flex items-center justify-center mx-auto mb-3 relative z-10"
              >
                <Icon 
                  icon={isCorrect ? "mdi:check-circle" : "mdi:information"} 
                  className="w-8 h-8 text-white" 
                />
              </motion.div>

              <motion.h2
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ delay: 0.2 }}
                className="text-2xl font-bold mb-1 relative text-yellow-700 z-10"
              >
                {isCorrect ? 'Excellent Work!' : 'Answer Review'}
              </motion.h2>

              <motion.p
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ delay: 0.3 }}
                className="text-yellow-600 text-sm relative z-10"
              >
                Question {currentQuestionIndex + 1} of {totalQuestions}
              </motion.p>
            </div>

            {/* Content */}
            <div className="p-6">
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.4 }}
                className="mb-6"
              >
                <h3 className="text-lg font-semibold text-gray-800 mb-3 text-center">
                  {isCorrect ? 'Perfect Answer!' : 'Correct Answer:'}
                </h3>

                <div className="bg-gradient-to-r from-yellow-50 to-orange-50 border-2 border-yellow-200 rounded-xl p-4">
                  <div
                    className="text-lg leading-relaxed text-gray-800"
                    dangerouslySetInnerHTML={{ __html: generateHighlightedSentence() }}
                  />
                </div>

                {!isCorrect && (
                  <motion.div
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    transition={{ delay: 0.6 }}
                    className="mt-4 p-3 bg-yellow-50 border border-yellow-200 rounded-lg"
                  >
                    <p className="text-sm text-yellow-800 text-center">
                      <Icon icon="mdi:lightbulb" className="inline mr-1" />
                      Red highlights show your answer, green highlights show the correct answers. Keep practicing!
                    </p>
                  </motion.div>
                )}
              </motion.div>

              {/* Action Buttons */}
              <div className={`flex flex-col sm:flex-row gap-3 ${isCorrect ? 'justify-center' : ''}`}>
                {/* Play Again Button - Only show when answer is incorrect */}
                {!isCorrect && (
                  <motion.button
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.5 }}
                    whileHover={{
                      scale: 1.03,
                      boxShadow: "0 8px 25px rgba(251, 191, 36, 0.4)"
                    }}
                    whileTap={{ scale: 0.97 }}
                    onClick={onPlayAgain}
                    className="flex-1 bg-gradient-to-r from-yellow-400 via-yellow-500 to-yellow-600 hover:from-yellow-500 hover:via-yellow-600 hover:to-yellow-700 text-white font-bold py-3 px-6 rounded-xl flex items-center justify-center gap-2 transition-all duration-300 shadow-lg border-2 border-yellow-300 relative overflow-hidden"
                  >
                    {/* Magical shimmer */}
                    <motion.div
                      className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent"
                      animate={{ x: [-100, 200] }}
                      transition={{ duration: 2, repeat: Infinity, repeatDelay: 3 }}
                    />
                    <Icon icon="mdi:refresh" className="text-xl relative z-10" />
                    <span className="relative z-10">Play Again</span>
                  </motion.button>
                )}

                {/* Next Challenge Button */}
                <motion.button
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: isCorrect ? 0.5 : 0.6 }}
                  whileHover={{
                    scale: 1.03,
                    boxShadow: "0 8px 25px rgba(251, 191, 36, 0.4)"
                  }}
                  whileTap={{ scale: 0.97 }}
                  onClick={onNextChallenge}
                  className={`${isCorrect ? 'w-full' : 'flex-1'} bg-gradient-to-r from-orange-400 via-orange-500 to-yellow-500 hover:from-orange-500 hover:via-orange-600 hover:to-yellow-600 text-white font-bold py-3 px-6 rounded-xl flex items-center justify-center gap-2 transition-all duration-300 shadow-lg border-2 border-orange-300 relative overflow-hidden`}
                >
                  {/* Magical shimmer */}
                  <motion.div
                    className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent"
                    animate={{ x: [-100, 200] }}
                    transition={{ duration: 2, repeat: Infinity, repeatDelay: 3, delay: 0.5 }}
                  />
                  <span className="relative z-10">
                    {isLastQuestion ? 'Submit Game' : 'Next Challenge'}
                  </span>
                  <Icon 
                    icon={isLastQuestion ? "mdi:trophy" : "material-symbols:arrow-forward-rounded"} 
                    className="text-xl relative z-10" 
                  />
                </motion.button>
              </div>
            </div>
          </motion.div>
        </div>
      )}
    </AnimatePresence>
  );
};

export default AnswerCheckModal;
