'use client';
import React, { useState } from 'react';
import Image from 'next/image';
import Link from 'next/link';
import useDataFetch from '@/hooks/useDataFetch';
import { Icon } from '@iconify/react';
import Modal from '@/components/Modal';
import Button from '@/components/Button';
import Pagination from '@/components/Pagination';
import api from '@/lib/api';
import { useQueryClient } from '@tanstack/react-query';
import PublishSkinForm from './_components/PublishSkinForm';
import { usePathname } from 'next/navigation';
import SkinStatusDropdown from './SkinStatusDropdown';

const SkinManagement = () => {
  const queryClient = useQueryClient();
  const pathname = usePathname();
  const [deleteModalOpen, setDeleteModalOpen] = useState(false);
  const [publishModalOpen, setPublishModalOpen] = useState(false);
  const [selectedSkin, setSelectedSkin] = useState(null);
  const [isDeleting, setIsDeleting] = useState(false);
  const [isPublishing, setIsPublishing] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(10);

  const { data, isLoading, error, refetch } = useDataFetch({
    queryKey: ['diary-skins', currentPage, itemsPerPage],
    endPoint: `/admin/diary/skins?page=${currentPage}&limit=${itemsPerPage}&sortBy=createdAt`,
  });

  // Fetch promotions for the publish form
  const { data: promotions } = useDataFetch({
    queryKey: 'promotions',
    endPoint: '/promotions/admin',
  });

  // Pagination handlers
  const handlePageChange = (page) => {
    setCurrentPage(page);
  };

  const handleItemsPerPageChange = (newItemsPerPage) => {
    setItemsPerPage(newItemsPerPage);
    setCurrentPage(1); // Reset to first page when changing items per page
  };

  const handleDeleteClick = (skin) => {
    setSelectedSkin(skin);
    setDeleteModalOpen(true);
  };

  const closeDeleteModal = () => {
    setDeleteModalOpen(false);
    setSelectedSkin(null);
  };

  const handlePublishClick = (skin) => {
    setSelectedSkin(skin);
    setPublishModalOpen(true);
  };

  const closePublishModal = () => {
    setPublishModalOpen(false);
    setSelectedSkin(null);
    setIsPublishing(false);
  };

  const handlePublishConfirm = async (values) => {
    if (!selectedSkin) return;

    try {
      setIsPublishing(true);

      // Convert metadata string to array if it's not empty
      if (values.metadata && typeof values.metadata === 'string') {
        values.metadata = values.metadata
          .split(',')
          .map((tag) => tag.trim())
          .filter((tag) => tag);
      }

      await api.post(
        `/admin/shop/diary-skins/${selectedSkin.id}/publish`,
        values
      );
      queryClient.invalidateQueries('diary-skins');
      refetch();
      closePublishModal();
    } catch (error) {
      console.error('Error publishing skin:', error);
    } finally {
      setIsPublishing(false);
    }
  };

  const handleDeleteConfirm = async () => {
    if (!selectedSkin) return;

    try {
      setIsDeleting(true);
      await api.delete(`/admin/diary/skins/${selectedSkin.id}`);
      queryClient.invalidateQueries('diary-skins');
      refetch();
      closeDeleteModal();
    } catch (error) {
      console.error('Error deleting skin:', error);
    } finally {
      setIsDeleting(false);
    }
  };

  if (isLoading) return <div>Loading...</div>;
  if (error) return <div>Error: {error.message}</div>;

  return (
    <div className="p-8 bg-gray-100 rounded-lg shadow-md">
      <div className="flex justify-between items-center mb-6">
        <div>
          <h1 className="text-2xl font-bold mb-2">Skin Management</h1>
          <p className="text-gray-600">Manage your diary skins here.</p>
        </div>
        <Link
          href="/dashboard/skins/add"
          className="bg-yellow-400 hover:bg-yellow-500 text-gray-800 px-4 py-2 rounded-md flex items-center gap-2"
        >
          <Icon icon="mdi:plus" />
          Add New Skin
        </Link>
      </div>
      <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-3">
        {data?.items.map((skin) => (
          <div key={skin.id} className="bg-white p-3 rounded-md shadow">
            <div className="relative">
              <Image
                src={skin.previewImagePath || '/assets/images/noImage.png'}
                alt={skin.name}
                width={150}
                height={50}
                layout="responsive"
                className="w-full h-24 object-cover rounded-md mb-2"
              />
              <div className="absolute top-1 right-1 flex gap-1">
                {!skin.isUsedIn && (
                  <Link
                    href={`/dashboard/skins/edit/${skin.id}`}
                    className="bg-white p-1 rounded-full shadow hover:bg-gray-100"
                    title="Edit Skin"
                  >
                    <Icon icon="mdi:pencil" className="text-gray-700 w-4 h-4" />
                  </Link>
                )}
                <button
                  onClick={() => handlePublishClick(skin)}
                  className="bg-white p-1 rounded-full shadow hover:bg-gray-100"
                  title="Publish Skin"
                >
                  <Icon
                    icon="ion:cloud-upload"
                    className="text-blue-500 w-4 h-4"
                  />
                </button>
                {skin.isUsedIn ? (
                  <div className="bg-white rounded-md shadow">
                    <SkinStatusDropdown
                      skinId={skin.id}
                      status={skin.isActive}
                      onSuccess={refetch}
                    />
                  </div>
                ) : (
                  <button
                    onClick={() => handleDeleteClick(skin)}
                    className="bg-white p-1 rounded-full shadow hover:bg-gray-100"
                    title="Delete Skin"
                  >
                    <Icon icon="mdi:delete" className="text-red-500 w-4 h-4" />
                  </button>
                )}
              </div>
            </div>
            <h2 className="text-base font-semibold truncate">{skin.name}</h2>
            <p className="text-xs text-gray-500 mb-1 h-8 overflow-hidden">
              {skin.description}
            </p>
            <div className="flex justify-between items-center">
              <div className="flex items-center gap-1.5">
                {skin.isUsedIn && (
                  <span className="text-xs px-2 py-0.5 rounded-full bg-orange-100 text-orange-800 font-medium">
                    In Use
                  </span>
                )}
                {skin.isGlobal && (
                  <span className="text-xs px-2 py-0.5 rounded-full bg-blue-100 text-blue-800 font-medium">
                    Global
                  </span>
                )}
              </div>
              <div className="text-xs text-gray-500">
                {skin.createdAt &&
                  new Date(skin.createdAt).toLocaleDateString()}
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Pagination */}
      {data && data.totalPages > 1 && (
        <div className="mt-6">
          <Pagination
            changePage={handlePageChange}
            currentPage={currentPage}
            totalItems={data.totalCount || 0}
            rowsPerPage={itemsPerPage}
            setRowsPerPage={handleItemsPerPageChange}
          />
        </div>
      )}

      {/* Delete Confirmation Modal */}
      <Modal
        isOpen={deleteModalOpen}
        onClose={closeDeleteModal}
        position="center"
        title="Delete Skin"
        width="md"
      >
        <div className="p-4">
          <div className="mb-6">
            <p className="text-gray-700">
              Are you sure you want to delete the skin{' '}
              <span className="font-semibold">{selectedSkin?.name}</span>?
            </p>
            <p className="text-red-500 text-sm mt-2">
              This action cannot be undone.
            </p>
          </div>

          <div className="flex justify-end space-x-3">
            <Button
              buttonText="Cancel"
              onClick={closeDeleteModal}
              className="w-auto bg-gray-200 text-gray-800 hover:bg-gray-300"
              type="button"
              disabled={isDeleting}
            />
            <Button
              buttonText={isDeleting ? 'Deleting...' : 'Delete'}
              onClick={handleDeleteConfirm}
              className="w-auto bg-red-500 text-white hover:bg-red-600"
              type="button"
              disabled={isDeleting}
            />
          </div>
        </div>
      </Modal>

      {/* Publish Skin Modal */}
      <Modal
        isOpen={publishModalOpen}
        onClose={closePublishModal}
        position="center"
        title="Publish Skin to Shop"
        width="lg"
      >
        <div className="p-4">
          <PublishSkinForm
            selectedSkin={selectedSkin}
            onSubmit={handlePublishConfirm}
            onCancel={closePublishModal}
            isPublishing={isPublishing}
            promotions={promotions}
          />
        </div>
      </Modal>
    </div>
  );
};

export default SkinManagement;
