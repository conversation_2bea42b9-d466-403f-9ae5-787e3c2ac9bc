'use client';
import React from 'react';
import { motion } from 'framer-motion';
import { Icon } from '@iconify/react';

const GameProgressHeader = ({
  initialData,
  currentQuestionIndex,
  hasTimeLimit,
  timeLeft,
  formatTimeDigital
}) => {
  return (
    <div className="flex items-center gap-5 relative z-20">
      {/* Enhanced Difficulty Display */}
      {initialData?.questions[currentQuestionIndex]?.level && (
        <motion.div 
          initial={{ scale: 0.8, opacity: 0 }}
          animate={{ scale: 1, opacity: 1 }}
          transition={{ duration: 0.6, ease: "easeOut", delay: 0.2 }}
          className="flex flex-col items-center space-x-1 bg-gradient-to-br from-yellow-200 via-orange-200 to-yellow-300 px-4 py-2 border-2 border-orange-400/50 backdrop-blur-sm rounded-xl shadow-lg"
        >
          <motion.span 
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.4 }}
            className="ml-2 text-lg font-bold text-orange-700 uppercase tracking-wide max-sm:hidden"
          >
             Difficulty 
          </motion.span>
          <div className="flex items-center">
            {[...Array(3)].map((_, i) => (
              <motion.div
                key={i}
                initial={{ scale: 0, rotate: -180 }}
                animate={{ scale: 1, rotate: 0 }}
                transition={{ duration: 0.4, delay: 0.5 + i * 0.1 }}
              >
                <Icon
                  icon="mdi:star"
                  className={`w-6 h-6 drop-shadow-sm transition-all duration-300 ${
                    i < initialData?.questions[currentQuestionIndex]?.level
                      ? 'text-orange-500 animate-pulse'
                      : 'text-gray-300'
                  }`}
                />
              </motion.div>
            ))}
          </div>
        </motion.div>
      )}

      {/* Enhanced Gamified Timer Display */}
      {hasTimeLimit && (
        <motion.div 
          initial={{ scale: 0.8, opacity: 0 }}
          animate={{ scale: 1, opacity: 1 }}
          transition={{ duration: 0.5, ease: "easeOut" }}
          className="flex justify-center"
        >
          <motion.div
            animate={{ 
              scale: timeLeft <= 10 ? [1, 1.05, 1] : 1,
              boxShadow: timeLeft <= 10 
                ? ['0 0 0 0 rgba(239, 68, 68, 0.4)', '0 0 0 10px rgba(239, 68, 68, 0)', '0 0 0 0 rgba(239, 68, 68, 0)']
                : '0 4px 20px rgba(234, 179, 8, 0.3)'
            }}
            transition={{ 
              duration: timeLeft <= 10 ? 1 : 0.3,
              repeat: timeLeft <= 10 ? Infinity : 0
            }}
            className={`inline-flex items-center p-2 sm:p-3 rounded-xl sm:text-4xl font-mono font-bold border sm:border-2 transition-all duration-300 ${
              timeLeft <= 10 
                ? 'border-red-500 text-red-600 bg-red-50' 
                : timeLeft <= 30 
                ? 'border-orange-500 text-orange-600 bg-orange-50'
                : 'border-green-500 text-green-600 bg-green-50'
            } shadow-lg`}
          >
            <motion.div
              transition={{ 
                rotate: { duration: 8, repeat: Infinity, ease: "linear" },
                scale: { duration: 0.5, repeat: timeLeft <= 10 ? Infinity : 0 }
              }}
            >
              <Icon 
                icon="streamline-freehand:time-stopwatch" 
                height='24'
                width='24'
                className="mr-2" 
              />
            </motion.div>
            
            <span
              className={`px-2 sm:px-3 sm:py-1 max-sm:text-sm rounded-lg backdrop-blur-sm mx-1 transition-colors duration-300 ${
                timeLeft <= 10 ? 'bg-red-200/90' : timeLeft <= 30 ? 'bg-orange-200/90' : 'bg-green-200/90'
              }`}
            >
              {formatTimeDigital(timeLeft).minutes}
            </span>
            
            <motion.span 
              className="mx-1 sm:text-2xl"
              // animate={{ 
              //   opacity: [1, 0.3, 1],
              //   scale: timeLeft <= 10 ? [1, 1.2, 1] : 1
              // }}
              // transition={{ 
              //   duration: 1, 
              //   repeat: Infinity,
              //   scale: { duration: 0.3, repeat: timeLeft <= 10 ? Infinity : 0 }
              // }}
            >
              :
            </motion.span>
            
            <span
              className={`px-2 sm:px-3 sm:py-1 max-sm:text-sm rounded-lg backdrop-blur-sm mx-1 transition-colors duration-300 ${
                timeLeft <= 10 ? 'bg-red-200/90' : timeLeft <= 30 ? 'bg-orange-200/90' : 'bg-green-200/90'
              }`}
            >
              {formatTimeDigital(timeLeft).seconds}
            </span>
          </motion.div>
        </motion.div>
      )}
    </div>
  );
};

export default GameProgressHeader;
