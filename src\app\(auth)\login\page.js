'use client';

import React, { useState } from 'react';
import { useDispatch } from 'react-redux';
import { useRouter, useSearchParams } from 'next/navigation';

import { loginSuccess } from '@/store/features/authSlice';
import api from '@/lib/api';
import { Formik, Form, Field } from 'formik';
import * as Yup from 'yup';
import FormInput from '@/components/form/FormInput';
import { Icon } from '@iconify/react';
import Link from 'next/link';

const LoginSchema = Yup.object().shape({
  userId: Yup.string().required('Please provide your user id'),
  password: Yup.string()
    .required('Please enter your password')
    .min(8, 'Password must be at least 8 characters'),
  selectedRole: Yup.string().required('Please select a role'),
});

const roles = [
  {
    id: 'student',
    title: 'Student',
    image: '/assets/images/auth/role-user.png',
  },
  {
    id: 'admin',
    title: 'Admin',
    image: '/assets/images/auth/role-admin.png',
  },
  {
    id: 'tutor',
    title: 'Tutor',
    image: '/assets/images/auth/role-mentor.png',
  },
];

export default function LoginPage() {
  const savedUserId = localStorage.getItem('userId');
  const savedPassword = localStorage.getItem('password');
  const savedSelectedRole = localStorage.getItem('selectedRole');

  const [showPassword, setShowPassword] = useState(false);
  const dispatch = useDispatch();
  const router = useRouter();
  const searchParams = useSearchParams();
  const returnTo = searchParams.get('returnTo');

  const handleLogin = async (values, { setSubmitting, setErrors }) => {
    // console.log(values);
    try {
      setSubmitting(true);

      const response = await api.post('/auth/login', values);
      // const profileImg = await api.get('/users/profile/picture');
      // console.log(response);

      if (response?.data?.access_token) {
        dispatch(
          loginSuccess({
            user: response?.data?.user,
            token: response?.data?.access_token,
            token_expires: response?.data?.token_expires,
          })
        );

        if (values?.rememberMe) {
          localStorage.setItem('userId', values.userId);
          localStorage.setItem('password', values.password);
          localStorage.setItem('selectedRole', values.selectedRole);
        } else {
          localStorage.removeItem('userId');
          localStorage.removeItem('password');
          localStorage.removeItem('selectedRole');
        }

        // await new Promise((resolve) => setTimeout(resolve, 100));
        if (returnTo) {
          router.push(returnTo);
          return;
        }
        if (
          response?.data?.user?.activePlan === null &&
          response?.data?.user?.selectedRole === 'student'
        ) {
          router.push('/pricing-plans');
          return;
        } else {
          if (
            response?.data?.user?.selectedRole === 'student' &&
            response?.data?.user?.activePlan
          ) {
            router.push('/diary');
          } else if (
            response?.data?.user?.selectedRole === 'tutor' ||
            response?.data?.user?.selectedRole === 'admin'
          ) {
            router.push('/dashboard');
          }
        }
      }
    } catch (error) {
      console.log(error);
      setErrors({
        general: error?.response?.data?.message || 'Login failed!',
      });
    }
    setSubmitting(false);
  };
  return (
    <div className="w-full max-w-2xl mx-auto">
      <div className="flex flex-col items-center">
        <div className="w-full max-w-md bg-white rounded-lg">
          <div className="px-4 py-6 pb-0 space-y-4">
            <h1 className="text-xl font-bold text-gray-900">
              HELLO ENGLISH COACHING - HEC
            </h1>

            {/* <p className="text-gray-600">Login Into Your Account</p> */}

            <Formik
              initialValues={{
                userId: savedUserId || '',
                password: savedPassword || '',
                rememberMe: false,
                selectedRole: savedSelectedRole || 'student',
              }}
              validationSchema={LoginSchema}
              onSubmit={handleLogin}
            >
              {({ isSubmitting, errors, values }) => (
                <Form className="space-y-4">
                  {errors.general && (
                    <div className="text-red-500 text-sm text-center">
                      {errors.general}
                    </div>
                  )}

                  {/* Role Selection */}
                  <div className="space-y-4">
                    <label className="block text-gray-700">
                      You're going to login as
                    </label>
                    <div className="flex gap-3">
                      {roles.map((role) => (
                        <label
                          key={role.id}
                          className="flex items-center cursor-pointer"
                        >
                          <Field
                            type="radio"
                            name="selectedRole"
                            value={role.id}
                            className="h-4 w-4 text-yellow-600 border-gray-300 focus:ring-yellow-500"
                          />
                          <span className="ml-2 text-sm text-gray-700">
                            {role.title}
                          </span>
                        </label>
                      ))}
                    </div>
                    {errors.selectedRole && (
                      <div className="text-red-500 text-sm">
                        {errors.selectedRole}
                      </div>
                    )}
                  </div>

                  <div className="">
                    <div className="w-full">
                      <FormInput
                        label="ID"
                        name="userId"
                        type="text"
                        placeholder="Enter your user id"
                        required
                      />
                    </div>
                    <div className="flex justify-end mt-1">
                      <Link
                        href="/forgot-userid"
                        className="text-sm text-red-500 hover:text-red-600"
                      >
                        Forgot ID?
                      </Link>
                    </div>
                  </div>

                  <div>
                    <div className="relative">
                      <FormInput
                        label="Password"
                        name="password"
                        type={showPassword ? 'text' : 'password'}
                        placeholder="Enter password"
                        required
                      />
                      <button
                        type="button"
                        onClick={() => setShowPassword(!showPassword)}
                        className="absolute right-3 top-[42px] transform text-gray-500"
                      >
                        <Icon
                          icon={showPassword ? 'mdi:eye-off' : 'mdi:eye'}
                          width="16"
                          height="16"
                        />
                      </button>
                    </div>
                    <div className="flex justify-end mt-1">
                      <Link
                        href="/forgot-password"
                        className="text-sm text-red-500 hover:text-red-600"
                      >
                        Forgot Password?
                      </Link>
                    </div>
                  </div>

                  <div className="flex items-center justify-between">
                    <label className="flex items-center cursor-pointer">
                      <Field
                        type="checkbox"
                        name="rememberMe"
                        className="h-4 w-4 text-blue-600 border-gray-300 rounded"
                      />
                      <span className="ml-2 text-sm text-gray-600">
                        Remember Me
                      </span>
                    </label>
                  </div>

                  <button
                    type="submit"
                    disabled={
                      isSubmitting ||
                      !values.userId ||
                      !values.password ||
                      !values.selectedRole
                    }
                    className={`w-full py-2 px-4 rounded-md transition-colors ${
                      isSubmitting ||
                      !values.userId ||
                      !values.password ||
                      !values.selectedRole
                        ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                        : 'bg-yellow-300 text-gray-800 hover:bg-yellow-400'
                    }`}
                  >
                    {isSubmitting ? 'Logging in...' : 'Login'}
                  </button>
                </Form>
              )}
            </Formik>

            <div className="text-center text-sm text-gray-600">
              Don't have an account?
              <Link
                href="/register"
                className="text-yellow-500 hover:text-yellow-600 ml-1"
              >
                Create Account
              </Link>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
