import React from 'react';
import { Icon } from '@iconify/react';
import { formatDistanceToNow } from 'date-fns';

// Map notification types to icons
const notificationIcons = {
  // New API notification types (lowercase)
  system: 'lucide:bell',
  tutor_assignment: 'lucide:user-plus',
  tutor_verification: 'lucide:user-check',
  chat_message: 'lucide:message-circle',
  novel_submission: 'lucide:book-open',
  diary_submission: 'lucide:book',
  essay_submission: 'lucide:file-text',
  mission_diary_submission: 'lucide:target',
  mission_essay_submission: 'lucide:scroll-text',
  diary_update: 'lucide:edit',
  mission_submission: 'lucide:target',
  story_submission: 'lucide:book-open-text',

  // Legacy notification types (uppercase)
  DIARY_COMMENT: 'lucide:message-square',
  DIARY_REVIEWED: 'lucide:check-circle',
  TUTOR_ASSIGNED: 'lucide:user-plus',
  TUTOR_GREETING: 'lucide:user',
  NEW_MESSAGE: 'lucide:mail',
  PLAN_PURCHASED: 'lucide:credit-card',
  ACCOUNT_VERIFIED: 'lucide:check',
  PASSWORD_RESET: 'lucide:key',
  ESSAY_SUBMITTED: 'lucide:file-text',
  ESSAY_REVIEWED: 'lucide:file-check',
  SYSTEM_ANNOUNCEMENT: 'lucide:bell',

  // Default icon for unknown types
  DEFAULT: 'lucide:bell',
};

const NotificationItem = ({ notification, onClick }) => {
  const {
    type,
    title,
    message,
    isRead, // API returns isRead instead of read
    createdAt
  } = notification;

  // Use isRead if available, otherwise fall back to read for backward compatibility
  const read = isRead !== undefined ? isRead : notification.read;

  // Get the appropriate icon for this notification type
  const iconName = notificationIcons[type] || notificationIcons.DEFAULT;

  // Format the time (e.g., "2 hours ago")
  const timeAgo = formatDistanceToNow(new Date(createdAt), { addSuffix: true });

  return (
    <div
      className={`p-3 sm:p-4 border-b border-gray-100 hover:bg-gray-50 cursor-pointer transition-colors ${
        !read ? 'bg-yellow-50' : ''
      }`}
      onClick={onClick}
    >
      <div className="flex items-start">
        <div className={`flex-shrink-0 mr-2 sm:mr-3 mt-1 ${!read ? 'text-yellow-500' : 'text-gray-400'}`}>
          <Icon icon={iconName} className="w-4 h-4 sm:w-5 sm:h-5" />
        </div>
        <div className="flex-1 min-w-0">
          <p className={`text-xs sm:text-sm font-medium ${!read ? 'text-gray-900' : 'text-gray-700'} leading-tight`}>
            {title}
          </p>
          <p className="text-xs sm:text-sm text-gray-500 mt-1 leading-tight">{message}</p>
          <p className="text-xs text-gray-400 mt-1">{timeAgo}</p>
        </div>
        {!read && (
          <div className="ml-1 sm:ml-2 flex-shrink-0">
            <span className="inline-block w-2 h-2 bg-yellow-500 rounded-full"></span>
          </div>
        )}
      </div>
    </div>
  );
};

export default NotificationItem;
