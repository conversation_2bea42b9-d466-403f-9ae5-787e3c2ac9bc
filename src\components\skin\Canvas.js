'use client';
import Image from 'next/image';
import { useDispatch, useSelector } from 'react-redux';
import { useRef, forwardRef, useImperativeHandle, useState } from 'react';
import ResizableWrapper from './ResizableWrapper.js';
import {
  selectCanvasItems,
  selectCanvasBackground,
  selectSelectedId,
  selectCanvasWidth,
  selectCanvasHeight,
  setSelectedId,
  clearSelection,
  addImageToCanvas,
} from '../../store/features/canvasSlice';

const Canvas = forwardRef((props, ref) => {
  const canvasRef = useRef(null);
  const [isDragOver, setIsDragOver] = useState(false);

  // Expose the canvas ref to parent components
  useImperativeHandle(ref, () => ({
    canvasRef,
  }));
  const dispatch = useDispatch();
  const canvasItemGlobal = useSelector(selectCanvasItems);
  const canvasItems = props?.canvasItems || canvasItemGlobal;
  const canvasBackground = useSelector(selectCanvasBackground);
  const selectedId = useSelector(selectSelectedId);
  const canvasWidth = useSelector(selectCanvasWidth);
  const canvasHeight = useSelector(selectCanvasHeight);
  // console.log('Canvas items:', canvasItems);
  const handleSelect = (id) => {
    // Always select the clicked item
    dispatch(setSelectedId(id));
  };

  // Helper to get topmost item under cursor
  const getTopmostItemId = (event) => {
    if (!canvasRef.current) return null;
    const rect = canvasRef.current.getBoundingClientRect();
    const x = event.clientX - rect.left;
    const y = event.clientY - rect.top;
    // Find all items under the cursor
    const itemsUnderCursor = canvasItems.filter((item) => {
      const s = item.styles || {};
      return x >= s.x && x <= s.x + s.width && y >= s.y && y <= s.y + s.height;
    });
    if (itemsUnderCursor.length === 0) return null;
    // Return the id of the item with the highest zIndex
    return itemsUnderCursor.reduce((top, item) => {
      if (!top) return item;
      return (item.zIndex || 1) >= (top.zIndex || 1) ? item : top;
    }, null).id;
  };

  const handleCanvasClick = (event) => {
    // Try to select the topmost item under the cursor
    const topId = getTopmostItemId(event);
    if (topId) {
      // If clicking on an element, select it
      dispatch(setSelectedId(topId));
    } else {
      // Only deselect when clicking on empty canvas area
      dispatch(clearSelection());
    }
  };

  const handleDrop = (e) => {
    e.preventDefault();
    setIsDragOver(false);
    
    const rect = canvasRef.current.getBoundingClientRect();
    const x = e.clientX - rect.left;
    const y = e.clientY - rect.top;
    
    try {
      const data = JSON.parse(e.dataTransfer.getData('text/plain'));
      
      if (data.type === 'image') {
        // Add image at drop position without selecting it
        dispatch(addImageToCanvas({
          imageSrc: data.src,
          id: `image-${Date.now()}`,
          styles: {
            x: x - 75, // Center the image on drop point
            y: y - 75,
            width: 150,
            height: 150,
          },
          zIndex: 1
        }));
      }
    } catch (error) {
      console.error('Error parsing drop data:', error);
    }
  };

  const handleDragOver = (e) => {
    e.preventDefault();
    setIsDragOver(true);
  };

  const handleDragLeave = (e) => {
    e.preventDefault();
    setIsDragOver(false);
  };
  // console.log(canvasItems, 'tst data');
  return (
    <div
      ref={canvasRef}
      onClick={handleCanvasClick}
      onDrop={handleDrop}
      onDragOver={handleDragOver}
      onDragLeave={handleDragLeave}
      style={{
        position: 'relative',
        width: canvasWidth,
        height: canvasHeight,
        background: canvasBackground,
        overflow: selectedId ? 'visible' : 'hidden', // Only allow overflow when an element is selected
        transition: 'all 0.3s ease',
        boxShadow: '0 4px 8px rgba(0,0,0,0.1)',
        margin: '0 auto',
        border: isDragOver ? '2px dashed #3b82f6' : '1px solid #e5e7eb',
      }}
    >
      {canvasItems.map((item) => (
        <ResizableWrapper
          key={item.id}
          id={item.id}
          styles={item.styles}
          selectedId={selectedId}
          onSelect={handleSelect}
          zIndex={item.zIndex}
          type={item.type}
        >
          {item.type === 'image' ? (
            <div
              style={{
                position: 'relative',
                width: '100%',
                height: '100%',
              }}
            >
              {item.image ? (
                <Image
                  src={item.image}
                  alt="Skin element"
                  fill
                  sizes="(max-width: 200px) 100vw"
                  style={{ objectFit: 'contain' }}
                  onError={(e) => {
                    console.error('Image failed to load:', item.image);
                    e.target.style.display = 'none';
                  }}
                />
              ) : (
                <div className="w-full h-full flex items-center justify-center bg-gray-100 text-gray-400 text-xs">
                  Image not available
                </div>
              )}
            </div>
          ) : (
            <div
              className={`w-full h-full ${
                item.textType === 'body'
                  ? 'flex-col custom-scrollbar'
                  : 'flex items-center'
              }`}
              style={{
                padding: '8px',
                color: item.styles?.color || '#000000',
                fontFamily: item.styles?.fontFamily || 'Roboto',
                fontSize: item.styles?.fontSize || '1rem',
                textAlign: item.styles?.textAlign || 'left',
                // For non-body text types, use flexbox justify-content based on textAlign
                ...(item.textType !== 'body' && {
                  justifyContent: 
                    item.styles?.textAlign === 'center' ? 'center' :
                    item.styles?.textAlign === 'right' ? 'flex-end' :
                    item.styles?.textAlign === 'justify' ? 'center' : // justify behaves like center in flex
                    'flex-start' // left alignment
                }),
                ...(item.textType === 'body' && {
                  overflowY: 'auto',
                  maxHeight: '100%',
                  transform: 'translateZ(0)',
                  willChange: 'scroll-position',
                  pointerEvents: 'auto',
                }),
              }}
            >
              {item.content}
            </div>
          )}
        </ResizableWrapper>
      ))}
    </div>
  );
});

// Add display name for forwardRef component
Canvas.displayName = 'Canvas';

export default Canvas;
