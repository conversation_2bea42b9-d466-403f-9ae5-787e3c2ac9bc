import React, { useState, useEffect, useRef } from 'react';
import Image from 'next/image';
import { Icon } from '@iconify/react';
import { motion, AnimatePresence } from 'framer-motion';

// Custom hook for floating animation
const useFloatingAnimation = (
  words,
  usedWords,
  disable,
  hoveredWord,
  selectedWord,
  draggedWord,
  isMobile
) => {
  const animationRefs = useRef({});
  const containerRef = useRef(null);
  const initializedWords = useRef(new Set());
  const [wordPositions, setWordPositions] = useState({});

  useEffect(() => {
    if (disable) return;

    const availableWordIndices = words.map((word, index) => ({ word, index })).filter(({ index }) => !usedWords.includes(index));

    // Initialize positions and velocities for new words
    const newPositions = {};
    availableWordIndices.forEach(({ word, index }) => {
      const wordKey = `${word}-${index}`;
      if (!initializedWords.current.has(wordKey)) {
        initializedWords.current.add(wordKey);
        // Gamified visual properties
        const shapes = [
          'rounded-full',
          'rounded-3xl',
          'rounded-2xl',
          'rounded-xl',
          'rounded-lg',
        ];
        const colors = [
          'linear-gradient(135deg, #ff6b6b, #feca57)',
          'linear-gradient(135deg, #a8e6cf, #3dd5f3)',
          'linear-gradient(135deg, #ff9ff3, #f368e0)',
          'linear-gradient(135deg, #54a0ff, #2e86de)',
          'linear-gradient(135deg, #5f27cd, #341f97)',
          'linear-gradient(135deg, #00d2d3, #01a3a4)',
          'linear-gradient(135deg, #feca57, #ff9ff3)',
          'linear-gradient(135deg, #48dbfb, #0abde3)',
          'linear-gradient(135deg, #1dd1a1, #10ac84)',
          'linear-gradient(135deg, #ff6348, #e17055)',
          'linear-gradient(135deg, #fd79a8, #e84393)',
          'linear-gradient(135deg, #fdcb6e, #e17055)',
        ];
        const shadowColors = ['drop-shadow-lg', 'drop-shadow-xl'];

        newPositions[wordKey] = {
          x: Math.random() * 80 + 10, // Random starting position (10-90%)
          y: Math.random() * 80 + 10,
          vx: (Math.random() - 0.5) * (isMobile ? 0.05 : 0.1), // Even slower on mobile
          vy: (Math.random() - 0.5) * (isMobile ? 0.05 : 0.1),
          phase: Math.random() * Math.PI * 2, // Random phase for sine wave
          amplitude: isMobile
            ? 0.05 + Math.random() * 0.03
            : 0.1 + Math.random() * 0.05, // Smaller on mobile
          frequency: isMobile
            ? 0.001 + Math.random() * 0.0005
            : 0.002 + Math.random() * 0.001, // Much slower on mobile
          // Visual properties
          shape: shapes[Math.floor(Math.random() * shapes.length)],
          color: colors[Math.floor(Math.random() * colors.length)],
          shadowColor:
            shadowColors[Math.floor(Math.random() * shadowColors.length)],
          scale: isMobile
            ? 1.0 + Math.random() * 0.1
            : 1.1 + Math.random() * 0.2, // Smaller scale on mobile
          rotation: Math.random() * 360, // Random initial rotation
          rotationSpeed: isMobile ? 0 : (Math.random() - 0.5) * 0.5, // No rotation on mobile
          pulsePhase: Math.random() * Math.PI * 2, // Random pulse phase
          pulseSpeed: 0.005 + Math.random() * 0.002, // Much slower pulse speed (0.005-0.007)
        };
      }
    });

    if (Object.keys(newPositions).length > 0) {
      setWordPositions((prev) => ({ ...prev, ...newPositions }));
    }

    // Animation loop
    const animate = () => {
      setWordPositions((prev) => {
        const updated = { ...prev };

        availableWordIndices.forEach(({ word, index }) => {
          const wordKey = `${word}-${index}`;
          if (updated[wordKey]) {
            const pos = updated[wordKey];
            const currentTime = Date.now();

            // Keep ALL words floating continuously - desktop-like behavior
            const shouldStopFloating = false; // All words continue floating regardless of drag state

            if (!shouldStopFloating) {
              // Add sine wave motion for more organic movement (reduced on mobile)
              const sineX =
                Math.sin(currentTime * pos.frequency + pos.phase) *
                pos.amplitude;
              const sineY =
                Math.cos(currentTime * pos.frequency + pos.phase * 1.3) *
                pos.amplitude;

              // Update position with velocity and sine wave (reduced intensity on mobile)
              const motionMultiplier = isMobile ? 0.02 : 0.05;
              pos.x += pos.vx + sineX * motionMultiplier;
              pos.y += pos.vy + sineY * motionMultiplier;

              // Update rotation (only on desktop)
              if (!isMobile) {
                pos.rotation += pos.rotationSpeed;
                if (pos.rotation > 360) pos.rotation -= 360;
                if (pos.rotation < 0) pos.rotation += 360;
              }
            }

            // Remove pulsing scale (blinking effect)
            pos.currentScale = pos.scale;

            // Bounce off boundaries with some damping
            const marginX = isMobile ? 10 : 4; // Horizontal margin
            const marginY = 15; // Vertical margin

            // Horizontal bounce
            if (pos.x <= marginX || pos.x >= 100 - marginX) {
              pos.vx *= -0.7; // Damping factor for realistic bounce
              pos.x = Math.max(marginX, Math.min(100 - marginX, pos.x));
              if (!isMobile) {
                pos.rotationSpeed += (Math.random() - 0.5) * 1;
              }
            }

            // Vertical bounce (margin is 0)
            if (pos.y <= marginY || pos.y >= 100 - marginY) {
              pos.vy *= -0.7;
              pos.y = Math.max(marginY, Math.min(100 - marginY, pos.y));
              if (!isMobile) {
                pos.rotationSpeed += (Math.random() - 0.5) * 1;
              }
            }

            // Add slight random perturbation to prevent getting stuck (reduced on mobile)
            const perturbation = isMobile ? 0.002 : 0.005;
            pos.vx += (Math.random() - 0.5) * perturbation;
            pos.vy += (Math.random() - 0.5) * perturbation;

            // Limit velocity to prevent too fast movement
            const maxVelocity = isMobile ? 0.04 : 0.08; // Even slower on mobile
            pos.vx = Math.max(-maxVelocity, Math.min(maxVelocity, pos.vx));
            pos.vy = Math.max(-maxVelocity, Math.min(maxVelocity, pos.vy));

            // Limit rotation speed (desktop only)
            if (!isMobile) {
              const maxRotationSpeed = 1; // Much slower rotation
              pos.rotationSpeed = Math.max(
                -maxRotationSpeed,
                Math.min(maxRotationSpeed, pos.rotationSpeed)
              );
            }
          }
        });

        return updated;
      });

      animationRefs.current.frameId = requestAnimationFrame(animate);
    };

    // Start animation
    animationRefs.current.frameId = requestAnimationFrame(animate);

    // Cleanup
    return () => {
      if (animationRefs.current.frameId) {
        cancelAnimationFrame(animationRefs.current.frameId);
      }
    };
  }, [words, usedWords, disable]); // Removed wordPositions from dependencies to prevent infinite loop

  return { wordPositions, containerRef };
};

const WordBlock = ({
  words = [],
  usedWords = [],
  title,
  subtitle,
  hide = false,
  disable = false,
  disableDrag = false,
  disableClick = false,
  dragType,
  onDragStart,
  onClick,
  showImage = true,
  imageSrc = '/assets/images/all-img/footer_butterfly.png',
  imageWidth = 50,
  imageHeight = 50,
  imageAlt = 'Icon',
  containerClassName = '',
  wordsContainerClassName = '',
  wordClassName = '',
  selectedWord = null,
  style = {},
}) => {
  const [draggedWord, setDraggedWord] = useState(null);
  const [touchStartPos, setTouchStartPos] = useState(null);
  const [hoveredWord, setHoveredWord] = useState(null);
  const [dragPosition, setDragPosition] = useState({ x: 0, y: 0 });
  const [isDragging, setIsDragging] = useState(false);
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });

  // Mobile detection
  const [isMobile, setIsMobile] = useState(false);

  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth <= 768 || 'ontouchstart' in window);
    };

    checkMobile();
    window.addEventListener('resize', checkMobile);

    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  const { wordPositions, containerRef } = useFloatingAnimation(
    words,
    usedWords,
    disable,
    hoveredWord,
    selectedWord,
    draggedWord,
    isMobile
  );

  const handleDragStart = (word, wordIndex) => (e) => {
    if (disable || disableDrag) {
      e.preventDefault();
      return;
    }

    // For desktop, set up drag ghost and tracking
    // if (!isMobile) {
    //   const rect = containerRef.current?.getBoundingClientRect();
    //   if (rect) {
    //     setMousePosition({
    //       x: e.clientX - rect.left,
    //       y: e.clientY - rect.top
    //     });
    //     setDraggedWord(word);
    //     setIsDragging(true);

    //     // Create a custom drag image (invisible)
    //     const dragImage = document.createElement('div');
    //     dragImage.style.opacity = '0';
    //     dragImage.style.position = 'absolute';
    //     dragImage.style.top = '-1000px';
    //     document.body.appendChild(dragImage);
    //     e.dataTransfer.setDragImage(dragImage, 0, 0);

    //     // Clean up drag image after a short delay
    //     setTimeout(() => {
    //       document.body.removeChild(dragImage);
    //     }, 0);
    //   }
    // }

    if (onDragStart) {
      onDragStart(word, dragType, wordIndex);
    }
  };

  const handleClick = (word, index) => (e) => {
    if (disable || disableClick) {
      e.preventDefault();
      return;
    }
    if (onClick) {
      onClick(word, index);
    }
  };

  const handleMouseEnter = (word) => {
    setHoveredWord(word);
  };

  const handleMouseLeave = () => {
    setHoveredWord(null);
  };

  // Desktop drag handlers
  const handleMouseMove = (e) => {
    if (!isMobile && draggedWord && isDragging) {
      const rect = containerRef.current?.getBoundingClientRect();
      if (rect) {
        setMousePosition({
          x: e.clientX - rect.left,
          y: e.clientY - rect.top,
        });
      }
    }
  };

  const handleDragEnd = () => {
    if (!isMobile && draggedWord) {
      setDraggedWord(null);
      setIsDragging(false);
      setMousePosition({ x: 0, y: 0 });
    }
  };

  // Add global mouse move listener for desktop drag
  useEffect(() => {
    if (!isMobile && isDragging) {
      document.addEventListener('mousemove', handleMouseMove);
      document.addEventListener('mouseup', handleDragEnd);
      document.addEventListener('dragend', handleDragEnd);

      return () => {
        document.removeEventListener('mousemove', handleMouseMove);
        document.removeEventListener('mouseup', handleDragEnd);
        document.removeEventListener('dragend', handleDragEnd);
      };
    }
  }, [isMobile, isDragging, draggedWord]);

  // Hide the component if explicitly set to hide OR if all words are used
  const shouldHide = hide || (words.length > 0 && usedWords.length === words.length);

  if (shouldHide) return null;

  // Touch event handlers for mobile drag and drop
  const handleTouchStart = (word, wordIndex) => (e) => {
    if (disable || disableDrag) return;

    const touch = e.touches[0];
    const rect = containerRef.current?.getBoundingClientRect();
    if (rect) {
      setTouchStartPos({ x: touch.clientX, y: touch.clientY });
      setDragPosition({
        x: touch.clientX - rect.left,
        y: touch.clientY - rect.top,
      });
      setDraggedWord(word);
      setIsDragging(true);

      if (onDragStart) {
        onDragStart(word, dragType, wordIndex);
      }
    }
  };

  const handleTouchMove = (e) => {
    if (!draggedWord || !touchStartPos) return;
    // e.preventDefault(); // Prevent scrolling

    const touch = e.touches[0];
    const rect = containerRef.current?.getBoundingClientRect();
    if (rect) {
      setDragPosition({
        x: touch.clientX - rect.left,
        y: touch.clientY - rect.top,
      });
    }
  };

  const handleTouchEnd = (e) => {
    if (!draggedWord || !touchStartPos) return;

    const touch = e.changedTouches[0];
    const elementBelow = document.elementFromPoint(
      touch.clientX,
      touch.clientY
    );

    // Find the closest drop zone
    const dropZone = elementBelow?.closest('[data-drop-zone]');
    if (dropZone) {
      const dropEvent = new CustomEvent('mobileDrop', {
        detail: { draggedWord, dragType },
      });
      dropZone.dispatchEvent(dropEvent);
    }

    setDraggedWord(null);
    setTouchStartPos(null);
    setIsDragging(false);
    setDragPosition({ x: 0, y: 0 });
  };

  const getWordClassName = () => {
    let baseClass =
      'px-2 py-1 sm:px-3 sm:py-1.5 lg:px-3 lg:py-1.5 bg-[#FFF8E6] border border-yellow-500 rounded-md text-xs';

    if (disable) {
      baseClass += ' opacity-50 cursor-not-allowed';
    } else if (!disableDrag && !disableClick) {
      baseClass +=
        ' cursor-grab active:cursor-grabbing shadow hover:cursor-pointer';
    } else if (!disableClick) {
      baseClass += ' cursor-pointer';
    }

    if (wordClassName) {
      baseClass += ` ${wordClassName}`;
    }

    return baseClass;
  };

  // Helper function to determine if word should be hidden during drag
  const isWordHiddenDuringDrag = (word, draggedWord, isDragging) => {
    return draggedWord === word && isDragging;
  };

  // Helper function to get dynamic word classes based on state
  const getWordDynamicClassName = (
    word,
    wordIndex,
    draggedWord,
    selectedWord,
    wordPositions,
    isMobile,
    isDragging
  ) => {
    const baseClass = getWordClassName();
    const isHidden = isWordHiddenDuringDrag(word, draggedWord, isDragging);
    const isDraggedWord = draggedWord === word;
    const isSelectedWord = selectedWord === word && draggedWord !== word;
    const wordKey = `${word}-${wordIndex}`;
    const hasFloatingPosition = wordPositions[wordKey];

    // Build dynamic classes array
    const classes = [baseClass];

    // Drag state classes - DESKTOP AND MOBILE UNIFIED
    if (isDraggedWord && isDragging) {
      classes.push('opacity-0 invisible'); // Hide original word during drag on both desktop and mobile
    } else if (isDraggedWord && !isDragging) {
      if (isMobile) {
        classes.push('opacity-90 z-50 dragging');
      } else {
        classes.push('opacity-100 z-50'); // Desktop normal state
      }
    }

    // Selection state
    if (isSelectedWord) {
      classes.push('ring-2 ring-yellow-500 bg-yellow-100 z-40');
    }

    // Position and transition classes
    if (hasFloatingPosition && !isHidden) {
      if (!isMobile) {
        classes.push('absolute transition-none');
      } else {
        classes.push('absolute transition-transform duration-300 ease-out');
      }
    } else if (!hasFloatingPosition) {
      classes.push('relative transition-all duration-200');
    }

    // Device-specific classes
    classes.push(isMobile ? 'cursor-default' : 'cursor-pointer');
    classes.push('select-none touch-manipulation');

    if (isMobile && hasFloatingPosition) {
      classes.push('mobile-floating-word');
    }

    return classes.join(' ');
  };

  // Helper function to get word styles based on state
  const getWordDynamicStyles = (
    word,
    wordIndex,
    draggedWord,
    selectedWord,
    wordPositions,
    isMobile,
    isDragging,
    hoveredWord
  ) => {
    const isHidden = isWordHiddenDuringDrag(word, draggedWord, isDragging);
    const wordKey = `${word}-${wordIndex}`;
    const hasFloatingPosition = wordPositions[wordKey];

    // Floating position styles
    if (hasFloatingPosition && !isHidden) {
      return {
        left: `${wordPositions[wordKey].x}%`,
        top: `${wordPositions[wordKey].y}%`,
        transform: isMobile
          ? `translate(-50%, -50%) scale(${
              wordPositions[wordKey].currentScale || wordPositions[wordKey].scale
            })`
          : `translate(-50%, -50%) rotate(${
              wordPositions[wordKey].rotation
            }deg) scale(${
              wordPositions[wordKey].currentScale || wordPositions[wordKey].scale
            })`,
        background: wordPositions[wordKey].color,
        zIndex: hoveredWord === word ? 30 : selectedWord === word ? 40 : 10,
        color: 'white',
        fontWeight: 'bold',
        fontSize: isMobile ? '12px' : '14px',
        textShadow: '0 2px 4px rgba(0,0,0,0.3)',
        border: '2px solid rgba(255,255,255,0.3)',
        boxShadow:
          hoveredWord === word || selectedWord === word
            ? '0 6px 20px rgba(0,0,0,0.3)'
            : '0 4px 15px rgba(0,0,0,0.2)',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        padding: isMobile ? '4px 8px' : '6px 12px',
        willChange: 'transform',
        backfaceVisibility: 'hidden',
        WebkitBackfaceVisibility: 'hidden',
        WebkitTapHighlightColor: 'transparent',
        overflow: 'hidden',
        whiteSpace: 'nowrap',
        textOverflow: 'ellipsis',
        flexShrink: 0,
        flexGrow: 0,
        position: 'absolute',
      };
    }

    // Non-floating styles
    if (isMobile) {
      return {
        WebkitTapHighlightColor: 'transparent',
        overflow: 'hidden',
        whiteSpace: 'nowrap',
        textOverflow: 'ellipsis',
        flexShrink: 0,
        flexGrow: 0,
        boxSizing: 'border-box',
        fontSize: '12px',
        padding: '4px 8px',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
      };
    }

    return {
      WebkitTapHighlightColor: 'transparent',
      fontSize: '14px',
      padding: '6px 12px',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
    };
  };

  const getContainerClassName = () => {
    let baseClass =
      'relative bg-white rounded-[32px] border-4 border-orange-300';

    if (containerClassName) {
      baseClass += ` ${containerClassName}`;
    }

    return baseClass;
  };

  const getWordsContainerClassName = () => {
    let baseClass = 'flex flex-wrap gap-2 sm:gap-3';

    if (wordsContainerClassName) {
      baseClass += ` ${wordsContainerClassName}`;
    }

    return baseClass;
  };

  return (
    <>
      {/* Mobile-specific CSS to prevent layout issues during drag */}
      {isMobile && (
        <style jsx>{`
          .mobile-floating-word {
            padding: 4px 8px !important;
            flex: none !important;
            box-sizing: border-box !important;
            overflow: hidden !important;
            white-space: nowrap !important;
            text-overflow: ellipsis !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
            user-select: none !important;
            -webkit-touch-callout: none !important;
            -webkit-tap-highlight-color: transparent !important;
            touch-action: none !important;
            position: absolute !important;
            font-size: 12px !important;
            display: flex !important;
            align-items: center !important;
            justify-content: center !important;
          }

          .mobile-floating-word:active,
          .mobile-floating-word:focus,
          .mobile-floating-word:hover,
          .mobile-floating-word.dragging {
            padding: 4px 8px !important;
            transform-origin: center center !important;
          }

          .mobile-floating-word.dragging {
            box-shadow: 0 8px 25px rgba(59, 130, 246, 0.4),
              0 0 0 3px rgba(59, 130, 246, 0.3) !important;
            border: 2px solid rgba(59, 130, 246, 0.6) !important;
            background: linear-gradient(135deg, #3b82f6, #1d4ed8) !important;
            color: white !important;
            font-weight: bold !important;
            animation: mobile-drag-pulse 1s ease-in-out infinite alternate !important;
          }

          @keyframes mobile-drag-pulse {
            0% {
              box-shadow: 0 8px 25px rgba(59, 130, 246, 0.4),
                0 0 0 3px rgba(59, 130, 246, 0.3);
            }
            100% {
              box-shadow: 0 12px 35px rgba(59, 130, 246, 0.6),
                0 0 0 5px rgba(59, 130, 246, 0.5);
            }
          }

          @keyframes desktop-drag-pulse {
            0% {
              box-shadow: 0 8px 25px rgba(59, 130, 246, 0.4),
                0 0 0 3px rgba(59, 130, 246, 0.3);
              transform: translate(-50%, -50%) scale(1);
            }
            100% {
              box-shadow: 0 12px 35px rgba(59, 130, 246, 0.6),
                0 0 0 5px rgba(59, 130, 246, 0.5);
              transform: translate(-50%, -50%) scale(1.05);
            }
          }

          .mobile-drag-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(
              135deg,
              rgba(59, 130, 246, 0.1),
              rgba(147, 51, 234, 0.1)
            );
            border: 2px dashed rgba(59, 130, 246, 0.4);
            border-radius: 12px;
            z-index: 15;
            pointer-events: none;
            backdrop-filter: blur(1px);
          }

          .mobile-drag-feedback {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: rgba(255, 255, 255, 0.95);
            padding: 8px 16px;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            font-size: 14px;
            font-weight: 600;
            color: #1e40af;
            white-space: nowrap;
            z-index: 16;
            pointer-events: none;
          }
        `}</style>
      )}

      <div className={getContainerClassName() + ' p-6 pb-0'} style={style}>
        {showImage && (
          <Image
            src={imageSrc}
            width={imageWidth}
            height={imageHeight}
            alt={imageAlt}
            className="absolute -right-5 -top-5"
          />
        )}
        {isMobile && (
          <h2 className="text-sm font-black text-yellow-600 mb-2 tracking-wide text-center drop-shadow-sm flex items-center gap-2 justify-center">
            Build Starting Sentenece
          </h2>
        )}
        {!isMobile && title && (
          <h2 className="text-lg sm:text-xl font-black text-yellow-600 mb-2 tracking-wide text-center drop-shadow-sm flex items-center gap-2 justify-center">
            <Icon
              icon="emojione-monotone:video-game"
              width="24"
              height="24"
              className="mt-1"
            />
            {title}
          </h2>
        )}
        {!isMobile && subtitle && (
          <p className="text-xs sm:text-base text-gray-700 mb-4 sm:mb-5 text-center bg-yellow-50 px-4 py-2 rounded-xl border border-yellow-300 shadow-sm">
            {subtitle}
          </p>
        )}
        <div
          ref={containerRef}
          className={`${getWordsContainerClassName()} relative ${
            isMobile ? 'min-h-[380px]' : 'min-h-[200px]'
          }`}
        >
          {/* Enhanced drag overlay for better UX - both mobile and desktop */}
          {draggedWord && isDragging && (
            <>
              {/* Drag ghost that follows cursor/finger */}
              <div
                className="absolute pointer-events-none z-50"
                style={{
                  left: `${isMobile ? dragPosition.x : mousePosition.x}px`,
                  top: `${isMobile ? dragPosition.y : mousePosition.y}px`,
                  transform: 'translate(-50%, -50%)',
                  background: 'linear-gradient(135deg, #3b82f6, #1d4ed8)',
                  color: 'white',
                  padding: isMobile ? '4px 8px' : '6px 12px',
                  borderRadius: '8px',
                  fontSize: isMobile ? '12px' : '14px',
                  fontWeight: 'bold',
                  boxShadow:
                    '0 8px 25px rgba(59, 130, 246, 0.6), 0 0 0 3px rgba(59, 130, 246, 0.3)',
                  border: '2px solid rgba(255, 255, 255, 0.3)',
                  animation: isMobile
                    ? 'mobile-drag-pulse 1s ease-in-out infinite alternate'
                    : 'desktop-drag-pulse 1s ease-in-out infinite alternate',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  whiteSpace: 'nowrap',
                  zIndex: 1000,
                }}
              >
                {draggedWord}
              </div>
            </>
          )}
          <AnimatePresence>
            {words.map((word, i) =>
              !usedWords.includes(i) ? (
                <motion.div
                  key={`${word}-${i}`}
                  initial={{ opacity: 0, scale: 0.8, y: -20 }}
                  animate={{ opacity: 1, scale: 1, y: 0 }}
                  exit={{ opacity: 0, scale: 0.8, y: 20 }}
                  transition={{
                    duration: 0.5,
                    delay: i * 0.05,
                    ease: 'easeInOut',
                  }}
                  draggable={!disable && !disableDrag}
                  onDragStart={handleDragStart(word, i)}
                  onDragEnd={!isMobile ? handleDragEnd : undefined}
                  onTouchStart={isMobile ? handleTouchStart(word, i) : undefined}
                  onTouchMove={isMobile ? handleTouchMove : undefined}
                  onTouchEnd={
                    isMobile
                      ? (e) => {
                          e.preventDefault();
                          e.stopPropagation();
                          handleTouchEnd(e);
                        }
                      : undefined
                  }
                  onClick={
                    !isMobile
                      ? handleClick(word, i)
                      : (e) => {
                          e.preventDefault();
                          e.stopPropagation();
                          return false;
                        }
                  }
                  onMouseEnter={() => !isMobile && handleMouseEnter(word)}
                  onMouseLeave={() => !isMobile && handleMouseLeave()}
                  className={getWordDynamicClassName(
                    word,
                    i,
                    draggedWord,
                    selectedWord,
                    wordPositions,
                    isMobile,
                    isDragging
                  )}
                  style={getWordDynamicStyles(
                    word,
                    i,
                    draggedWord,
                    selectedWord,
                    wordPositions,
                    isMobile,
                    isDragging,
                    hoveredWord
                  )}
                  whileTap={undefined}
                  title={
                    !disable && !disableDrag
                      ? isMobile
                        ? 'Drag to place'
                        : 'Drag and Drop'
                      : ''
                  }
                >
                  {word}
                </motion.div>
              ) : null
            )}
          </AnimatePresence>
        </div>
      </div>
    </>
  );
};

export default WordBlock;
