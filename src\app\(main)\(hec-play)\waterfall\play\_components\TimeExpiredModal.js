import React from 'react';
import { Icon } from '@iconify/react';
import { motion, AnimatePresence } from 'framer-motion';

const TimeExpiredModal = ({ isOpen, onSkip, onPlayAgain, currentQuestion, totalQuestions }) => {
  if (!isOpen) return null;

  return (
    <AnimatePresence>
      {isOpen && (
        <div className="fixed inset-0 z-50 flex items-center justify-center p-4">
          {/* Compact Backdrop */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="absolute inset-0 bg-black/50 backdrop-blur-sm"
          />

          {/* Compact Magical Modal */}
          <motion.div
            initial={{ opacity: 0, scale: 0.8, y: 20 }}
            animate={{ opacity: 1, scale: 1, y: 0 }}
            exit={{ opacity: 0, scale: 0.8, y: 20 }}
            transition={{ type: "spring", duration: 0.4 }}
            className="relative bg-gradient-to-br from-yellow-50 via-orange-50 to-yellow-100 rounded-2xl shadow-xl max-w-sm w-full border-2 border-yellow-400"
          >
            {/* Magical Header */}
            <div className="bg-gradient-to-r from-yellow-400 via-yellow-500 to-yellow-600 p-4 text-white text-center rounded-t-2xl relative overflow-hidden">
              {/* Magical shimmer effect */}
              <motion.div
                className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent"
                animate={{ x: [-100, 200] }}
                transition={{ duration: 3, repeat: Infinity, repeatDelay: 2 }}
              />

              <motion.div
                initial={{ scale: 0 }}
                animate={{ scale: 1 }}
                transition={{ delay: 0.1, type: "spring" }}
                className="w-12 h-12 bg-white/20 rounded-full flex items-center justify-center mx-auto mb-2 relative z-10"
              >
                <Icon icon="mdi:clock-alert" className="w-6 h-6 text-white" />
              </motion.div>

              <motion.h2
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ delay: 0.2 }}
                className="text-xl font-bold mb-1 relative z-10"
              >
                Time's Up! 
              </motion.h2>

              <motion.p
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ delay: 0.3 }}
                className="text-yellow-100 text-sm relative z-10"
              >
                 Question {currentQuestion} of {totalQuestions}
              </motion.p>
            </div>

            {/* Compact Content */}
            <div className="p-4">
              <motion.p
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ delay: 0.4 }}
                className="text-center text-yellow-700 text-sm mb-4 font-medium"
              >
                🔮 Choose your magical path: 🔮
              </motion.p>

              {/* Magical Action Buttons */}
              <div className="space-y-3">
                {/* Magical Try Again Button */}
                <motion.button
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.5 }}
                  whileHover={{
                    scale: 1.03,
                    boxShadow: "0 8px 25px rgba(251, 191, 36, 0.4)"
                  }}
                  whileTap={{ scale: 0.97 }}
                  onClick={onPlayAgain}
                  className="w-full bg-gradient-to-r from-yellow-400 via-yellow-500 to-yellow-600 hover:from-yellow-500 hover:via-yellow-600 hover:to-yellow-700 text-white font-bold py-3 px-4 rounded-xl flex items-center justify-center gap-2 transition-all duration-300 shadow-lg border-2 border-yellow-300 relative overflow-hidden"
                >
                  {/* Magical shimmer */}
                  <motion.div
                    className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent"
                    animate={{ x: [-100, 200] }}
                    transition={{ duration: 2, repeat: Infinity, repeatDelay: 3 }}
                  />
                  <span className="relative z-10"> Try Again</span>
                </motion.button>

                {/* Magical Skip Button */}
                <motion.button
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.6 }}
                  whileHover={{
                    scale: 1.03,
                    boxShadow: "0 8px 25px rgba(180, 83, 9, 0.3)"
                  }}
                  whileTap={{ scale: 0.97 }}
                  onClick={onSkip}
                  className="w-full bg-gradient-to-r from-orange-500 via-orange-600 to-yellow-600 hover:from-orange-600 hover:via-orange-700 hover:to-yellow-700 text-white font-bold py-3 px-4 rounded-xl flex items-center justify-center gap-2 transition-all duration-300 shadow-lg border-2 border-orange-300 relative overflow-hidden"
                >
                  {/* Magical shimmer */}
                  <motion.div
                    className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent"
                    animate={{ x: [-100, 200] }}
                    transition={{ duration: 2, repeat: Infinity, repeatDelay: 3, delay: 0.5 }}
                  />
                  <span className="relative z-10">
                    {currentQuestion < totalQuestions ? ' Skip Question' : '🏆 Finish Game'}
                  </span>
                </motion.button>
              </div>

            </div>
          </motion.div>
        </div>
      )}
    </AnimatePresence>
  );
};

export default TimeExpiredModal;
