import React from 'react';
import { ButtonIcon } from '@/components/Button';
import { Icon } from '@iconify/react';

const DiaryPagination = ({
  hasEntries,
  isOpen,
  currentIndex,
  totalEntries,
  currentPage = 1,
  totalPages = 1,
  itemsPerPage = 10,
  isMobile = false,
  onLeftClick,
  onRightClick,
}) => {
  // Calculate current page entries for display
  const currentPageEntries = Math.min(
    itemsPerPage,
    totalEntries - (currentPage - 1) * itemsPerPage
  );

  // Navigation step: 1 for mobile, 2 for desktop
  const step = isMobile ? 1 : 2;

  // Determine if navigation arrows should be disabled
  const isLeftDisabled =
    (currentIndex === 0 && currentPage === 1) || !hasEntries || !isOpen;
  const isRightDisabled =
    !hasEntries ||
    (isOpen &&
      currentIndex + step >= currentPageEntries &&
      currentPage >= totalPages);

  return (
    <>
      {hasEntries && isOpen && (
        <>
          <div className={`absolute bottom-1/2 lg:bottom-[15px] ${isMobile ? '-left-3' : '-left-5'} lg:left-[-50px]`}>
            <div className="w-8 h-8">
              {isMobile ? (
                <button className='focus:ring-1 focus:ring-gray-400 rounded-full' onClick={!isLeftDisabled ? onLeftClick : undefined}>
                  <Icon
                    icon="line-md:chevron-left"
                    className={`w-5 h-5 ${
                      isLeftDisabled
                        ? 'opacity-50 cursor-not-allowed'
                        : 'cursor-pointer'
                    }`}
                  />
                </button>
              ) : (
                <ButtonIcon
                  icon="mdi:arrow-left"
                  innerBtnCls={`h-12 w-12 ${
                    isLeftDisabled
                      ? 'opacity-50 cursor-not-allowed'
                      : 'cursor-pointer'
                  }`}
                  btnIconCls="h-5 w-5"
                  aria-label="left navigation"
                  onClick={!isLeftDisabled ? onLeftClick : undefined}
                />
              )}
            </div>
          </div>

          <div className={`absolute bottom-1/2 lg:bottom-[15px] mb-2 ${isMobile? '-right-5' : '-right-0'} lg:right-[-40px]`}>
            <div className="max-sm:w-6 w-8 max-sm:h-6 h-8">
              {isMobile ? (
                <button className='focus:ring-1 focus:ring-gray-400 rounded-full' onClick={!isRightDisabled ? onRightClick : undefined}>
                  <Icon
                    icon="line-md:chevron-right"
                    className={`w-5 h-5 ${
                      isRightDisabled
                        ? 'opacity-50 cursor-not-allowed'
                        : 'cursor-pointer'
                    }`}
                  />
                </button>
              ) : (
                <ButtonIcon
                  icon="mdi:arrow-right"
                  innerBtnCls={`h-12 w-12 ${
                    isRightDisabled
                      ? 'opacity-50 cursor-not-allowed'
                      : 'cursor-pointer'
                  }`}
                  btnIconCls="h-5 w-5"
                  aria-label="right navigation"
                  onClick={!isRightDisabled ? onRightClick : undefined}
                />
              )}
            </div>
          </div>

          {isOpen && (
            <div className="absolute max-h-6 -top-5 lg:-bottom-5 left-1/2 transform -translate-x-1/2 text-xs bg-white px-3 py-1 rounded-full shadow-sm border border-gray-200">
              {isMobile ? (
                // Mobile: Show single entry
                <>
                  Entry {(currentPage - 1) * itemsPerPage + currentIndex + 1} of{' '}
                  {totalEntries}
                </>
              ) : // Desktop: Show entry range or single entry
              currentIndex + 1 < currentPageEntries ? (
                <>
                  Entry {(currentPage - 1) * itemsPerPage + currentIndex + 1}-
                  {(currentPage - 1) * itemsPerPage +
                    Math.min(currentIndex + 2, currentPageEntries)}{' '}
                  of {totalEntries}
                </>
              ) : (
                <>
                  Entry {(currentPage - 1) * itemsPerPage + currentIndex + 1} of{' '}
                  {totalEntries}
                </>
              )}
            </div>
          )}
        </>
      )}
    </>
  );
};

export default DiaryPagination;
