'use client';
import GoBack from '@/components/shared/GoBack';
import useDataFetch from '@/hooks/useDataFetch';
import React from 'react';
import { Icon } from '@iconify/react';
import { motion } from 'framer-motion';
import WaterfallGameMain from './_components/WaterfallGameMainRefactored';
import Image from 'next/image';

const WaterPlay = () => {
  const { data, isLoading, error, refetch } = useDataFetch({
    queryKey: ['waterfall-game'],
    endPoint: '/play/waterfall/new-game',
  });

  // Loading state
  if (isLoading) {
    return (
      <div className="relative min-h-screen overflow-hidden">
        {/* Sky background for loading */}
        <div className="fixed inset-0 bg-gradient-to-b from-sky-100 via-blue-50 to-white opacity-70" />

        <div className="relative z-10 max-w-7xl mx-auto p-5 xl:px-0 flex justify-center items-center min-h-[60vh]">
          <GoBack
            title={'HEC Play'}
            linkClass="absolute top-5 left-5 w-full max-w-40"
          />
          <div className="flex flex-col items-center">
            <Icon
              icon="eos-icons:loading"
              className="text-blue-500 text-5xl animate-spin"
            />
            <p className="mt-4 text-gray-700">Loading magical questions...</p>
          </div>
        </div>
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <div className="relative min-h-screen overflow-hidden">
        {/* Sky background for error */}
        <div className="fixed inset-0 bg-gradient-to-b from-sky-100 via-blue-50 to-white opacity-70" />

        <div className="relative z-10 max-w-7xl mx-auto p-5 xl:px-0">
          <GoBack title={'HEC Play'} linkClass="my-5 w-full max-w-40" />
          <div className="bg-red-50/90 backdrop-blur-sm border border-red-200 rounded-lg p-6 text-center">
            <Icon
              icon="material-symbols:error"
              className="text-red-500 text-5xl mx-auto"
            />
            <h2 className="text-xl font-semibold mt-4 text-red-700">
              Error Loading Game
            </h2>
            <p className="text-red-600 mt-2 mb-4">
              There was a problem loading the game. Please try again.
            </p>
            <button
              onClick={() => refetch()}
              className="px-4 py-2 bg-red-100 hover:bg-red-200 text-red-700 rounded-md transition-colors"
            >
              Try Again
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="relative min-h-screen overflow-hidden">
      {/* Enhanced Magical Sky Background - Covers entire page */}
      <div className="fixed top-[52px] lg:top-[105px] inset-0 pointer-events-none overflow-hidden z-0">
        {/* Enhanced sky gradient background */}
        <div className="absolute inset-0 bg-gradient-to-b from-yellow-200 via-yellow-100 to-white opacity-80" />

        {/* Additional sky depth layer */}
        <div className="absolute inset-0 bg-gradient-to-br from-blue-100/50 via-transparent to-purple-50/30" />

        {/* Enhanced Cloud SVG Background - Optimized */}
        <motion.svg
          className="absolute -top-20 -left-16 w-[130%] h-48 opacity-60"
          viewBox="0 0 700 180"
          fill="none"
          animate={{
            x: [0, 15, 0],
            y: [0, -8, 0],
          }}
          transition={{
            duration: 30,
            repeat: Infinity,
            ease: 'linear',
          }}
          style={{
            filter: 'drop-shadow(0 4px 8px rgba(59, 130, 246, 0.2))',
            willChange: 'transform',
          }}
        >
          {/* Large fluffy cloud */}
          <path
            d="M80 120 C50 120, 30 95, 55 75 C50 45, 80 35, 105 55 C130 25, 180 35, 190 75 C215 65, 230 90, 205 110 C230 130, 215 150, 180 140 L80 140 Z"
            fill="url(#skyCloudGradient1)"
            stroke="rgba(59, 130, 246, 0.8)"
            strokeWidth="3"
          />

          {/* Medium cloud */}
          <path
            d="M320 100 C295 100, 285 80, 305 70 C300 55, 325 50, 345 65 C365 45, 405 55, 415 75 C440 70, 450 90, 430 105 C450 120, 440 135, 405 125 L320 125 Z"
            fill="url(#skyCloudGradient2)"
            stroke="rgba(147, 51, 234, 0.8)"
            strokeWidth="3"
          />

          {/* Small cloud */}
          <path
            d="M520 85 C500 85, 495 70, 510 62 C505 50, 525 46, 540 58 C555 45, 575 50, 580 65 C600 60, 608 75, 590 85 C608 95, 600 108, 575 100 L520 100 Z"
            fill="url(#skyCloudGradient3)"
            stroke="rgba(239, 68, 68, 0.8)"
            strokeWidth="3"
          />

          {/* Additional background clouds */}
          <path
            d="M200 70 C185 70, 182 60, 192 55 C188 45, 202 42, 212 52 C222 40, 240 45, 245 58 C258 55, 263 68, 250 75 C263 82, 258 92, 240 87 L200 87 Z"
            fill="url(#skyCloudGradient4)"
            stroke="rgba(245, 158, 11, 0.7)"
            strokeWidth="2.5"
          />

          {/* Extra sky clouds for depth */}
          <path
            d="M600 110 C585 110, 582 98, 592 92 C588 82, 602 79, 612 87 C622 75, 635 79, 640 92 C650 89, 655 100, 645 107 C655 114, 650 122, 635 117 L600 117 Z"
            fill="url(#skyCloudGradient5)"
            stroke="rgba(34, 197, 94, 0.7)"
            strokeWidth="2"
          />

          {/* Distant clouds */}
          <path
            d="M30 55 C22 55, 20 47, 26 43 C23 36, 32 34, 38 40 C44 32, 54 35, 57 43 C64 41, 67 48, 60 53 C67 57, 64 63, 54 60 L30 60 Z"
            fill="url(#skyCloudGradient6)"
            stroke="rgba(251, 146, 60, 0.6)"
            strokeWidth="1.5"
          />

          {/* Enhanced Gradient definitions with better visibility */}
          <defs>
            <linearGradient
              id="skyCloudGradient1"
              x1="0%"
              y1="0%"
              x2="100%"
              y2="100%"
            >
              <stop offset="0%" stopColor="rgba(255, 255, 255, 0.9)" />
              <stop offset="30%" stopColor="rgba(219, 234, 254, 0.85)" />
              <stop offset="70%" stopColor="rgba(147, 197, 253, 0.7)" />
              <stop offset="100%" stopColor="rgba(59, 130, 246, 0.5)" />
            </linearGradient>
            <linearGradient
              id="skyCloudGradient2"
              x1="0%"
              y1="0%"
              x2="100%"
              y2="100%"
            >
              <stop offset="0%" stopColor="rgba(255, 255, 255, 0.9)" />
              <stop offset="30%" stopColor="rgba(233, 213, 255, 0.85)" />
              <stop offset="70%" stopColor="rgba(196, 181, 253, 0.7)" />
              <stop offset="100%" stopColor="rgba(147, 51, 234, 0.5)" />
            </linearGradient>
            <linearGradient
              id="skyCloudGradient3"
              x1="0%"
              y1="0%"
              x2="100%"
              y2="100%"
            >
              <stop offset="0%" stopColor="rgba(255, 255, 255, 0.9)" />
              <stop offset="30%" stopColor="rgba(254, 215, 215, 0.85)" />
              <stop offset="70%" stopColor="rgba(252, 165, 165, 0.7)" />
              <stop offset="100%" stopColor="rgba(239, 68, 68, 0.5)" />
            </linearGradient>
            <linearGradient
              id="skyCloudGradient4"
              x1="0%"
              y1="0%"
              x2="100%"
              y2="100%"
            >
              <stop offset="0%" stopColor="rgba(255, 255, 255, 0.9)" />
              <stop offset="30%" stopColor="rgba(254, 240, 138, 0.8)" />
              <stop offset="70%" stopColor="rgba(251, 191, 36, 0.6)" />
              <stop offset="100%" stopColor="rgba(245, 158, 11, 0.4)" />
            </linearGradient>
            <linearGradient
              id="skyCloudGradient5"
              x1="0%"
              y1="0%"
              x2="100%"
              y2="100%"
            >
              <stop offset="0%" stopColor="rgba(255, 255, 255, 0.9)" />
              <stop offset="30%" stopColor="rgba(209, 250, 229, 0.8)" />
              <stop offset="70%" stopColor="rgba(167, 243, 208, 0.6)" />
              <stop offset="100%" stopColor="rgba(34, 197, 94, 0.4)" />
            </linearGradient>
            <linearGradient
              id="skyCloudGradient6"
              x1="0%"
              y1="0%"
              x2="100%"
              y2="100%"
            >
              <stop offset="0%" stopColor="rgba(255, 255, 255, 0.9)" />
              <stop offset="30%" stopColor="rgba(255, 247, 237, 0.8)" />
              <stop offset="70%" stopColor="rgba(254, 215, 170, 0.6)" />
              <stop offset="100%" stopColor="rgba(251, 146, 60, 0.4)" />
            </linearGradient>
          </defs>
        </motion.svg>

        {/* Second layer of clouds for depth - Optimized */}
        <motion.svg
          className="absolute -top-12 -right-12 w-[120%] h-44 opacity-45"
          viewBox="0 0 650 160"
          fill="none"
          animate={{
            x: [0, -20, 0],
            y: [0, 10, 0],
          }}
          transition={{
            duration: 35,
            repeat: Infinity,
            ease: 'linear',
          }}
          style={{
            filter: 'drop-shadow(0 2px 4px rgba(147, 51, 234, 0.15))',
            willChange: 'transform',
          }}
        >
          <path
            d="M140 130 C115 130, 100 110, 120 95 C115 75, 140 70, 160 85 C180 65, 220 70, 230 95 C250 90, 265 110, 245 125 C265 140, 250 155, 220 150 L140 150 Z"
            fill="url(#skyCloudGradient2)"
            stroke="rgba(147, 51, 234, 0.7)"
            strokeWidth="2.5"
          />
          <path
            d="M450 110 C430 110, 422 95, 435 87 C430 75, 445 72, 460 82 C475 70, 495 75, 500 87 C515 84, 522 97, 510 105 C522 113, 515 123, 495 118 L450 118 Z"
            fill="url(#skyCloudGradient3)"
            stroke="rgba(239, 68, 68, 0.7)"
            strokeWidth="2.5"
          />
        </motion.svg>

        {/* Optimized floating magical particles - Reduced count */}
        {[...Array(8)].map((_, i) => (
          <motion.div
            key={i}
            className="absolute text-blue-400 text-sm opacity-50 pointer-events-none font-bold"
            animate={{
              y: [0, 100, 0],
              opacity: [0.2, 0.6, 0.2],
              rotate: [0, 360],
            }}
            transition={{
              duration: 12 + i * 2,
              repeat: Infinity,
              delay: i * 1.5,
              ease: 'linear',
            }}
            style={{
              left: `${5 + i * 12}%`,
              top: `${3 + (i % 4) * 8}%`,
              willChange: 'transform, opacity',
            }}
          >
            {['✨', '⭐', '💫', '🌟', '💎', '🔮', '🌠', '✨'][i]}
          </motion.div>
        ))}

        {/* Optimized word letters floating down - Reduced animation complexity */}
        {['W', 'O', 'R', 'D', 'S'].map((letter, i) => (
          <motion.div
            key={`letter-${i}`}
            className="absolute text-purple-400 text-lg opacity-40 pointer-events-none font-bold"
            animate={{
              y: [-20, 150],
              opacity: [0, 0.5, 0],
            }}
            transition={{
              duration: 15 + i * 2,
              repeat: Infinity,
              delay: i * 4,
              ease: 'linear',
            }}
            style={{
              left: `${15 + i * 15}%`,
              top: '0%',
              willChange: 'transform, opacity',
            }}
          >
            {letter}
          </motion.div>
        ))}

        {/* Optimized magical rain effect - Reduced count and complexity */}
        {[...Array(12)].map((_, i) => (
          <motion.div
            key={`sky-rain-${i}`}
            className="absolute w-1 h-8 bg-gradient-to-b from-blue-400/40 via-purple-300/30 to-transparent rounded-full"
            animate={{
              y: [-30, 120],
              opacity: [0, 0.6, 0],
            }}
            transition={{
              duration: 8 + i * 0.5,
              repeat: Infinity,
              delay: i * 0.7,
              ease: 'linear',
            }}
            style={{
              left: `${5 + i * 8}%`,
              top: '0%',
              willChange: 'transform, opacity',
            }}
          />
        ))}

        {/* Optimized sparkle rain - Reduced count and rotation */}
        {[...Array(6)].map((_, i) => (
          <motion.div
            key={`sparkle-rain-${i}`}
            className="absolute text-yellow-400 text-sm opacity-50 pointer-events-none"
            animate={{
              y: [-20, 120],
              opacity: [0, 0.7, 0],
              rotate: [0, 180],
            }}
            transition={{
              duration: 10 + i * 1.5,
              repeat: Infinity,
              delay: i * 3,
              ease: 'linear',
            }}
            style={{
              left: `${10 + i * 15}%`,
              top: '0%',
              willChange: 'transform, opacity',
            }}
          >
            ✨
          </motion.div>
        ))}

        {/* Enhanced sky gradient overlay with more depth */}
        <div className="absolute inset-0 bg-gradient-to-b from-sky-100/50 via-blue-50/30 to-transparent" />

        {/* Additional atmospheric effect */}
        <div className="absolute inset-0 bg-gradient-radial from-transparent via-white/8 to-transparent" />
      </div>

      <div className="relative z-10 max-w-7xl mb-8 mx-auto px-5 xl:px-0">
        <GoBack title={'HEC Waterfall Play'} linkClass="my-5 w-full max-w-60" />

        {/* Waterfall Game */}
        <WaterfallGameMain initialData={data} refetchGame={refetch} />
      </div>

      <div className="absolute right-0 bottom-0 z-0 pointer-events-none">
        <Image
          src={'/assets/images/all-img/cat2.png'}
          alt={'cat'}
          width={400}
          height={500}
          className="max-w-[190px] sm:max-w-[550px] h-auto opacity-80"
        />
      </div>
    </div>
  );
};

export default WaterPlay;
