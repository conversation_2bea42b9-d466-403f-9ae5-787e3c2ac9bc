'use client';
import React from 'react';
import { motion } from 'framer-motion';
import { DroppableBlank } from './WaterfallGame2';

const MagicalSentenceDisplay = ({
  parts,
  blanks,
  currentQuestion,
  handleResetBlank,
}) => {
  return (
    <div>
      <motion.div className="max-w-sm text-center mx-auto text-sm font-bold px-6 py-2 pb-4 rounded-full bg-gradient-to-r from-purple-600 via-pink-600 to-indigo-600 bg-clip-text text-transparent whitespace-nowrap pointer-events-none select-none">
        Drop the cathed words
      </motion.div>
      <div className="text-sm sm:text-base md:text-lg flex flex-wrap gap-1 sm:gap-2 items-center leading-relaxed justify-center text-center">
        {parts?.map((part, index) => (
          <React.Fragment key={index}>
            {/* Enhanced Magical Text Parts */}
            <motion.span
              initial={{ opacity: 0, y: 10 }}
              animate={{
                opacity: 1,
                y: 0,
              }}
              transition={{
                duration: 0.5,
                delay: index * 0.08,
                ease: 'easeOut',
              }}
              className="font-bold bg-gradient-to-r from-purple-700 via-indigo-600 to-purple-800 bg-clip-text text-transparent relative inline-block cursor-default select-none"
              style={{
                willChange: 'transform, opacity',
              }}
            >
              {part}

              {/* Enhanced magical glow effect */}
              {/* <motion.span 
              className="absolute inset-0 bg-gradient-to-r from-purple-400/40 via-pink-400/40 to-indigo-400/40 blur-xl -z-10 rounded-lg"
              animate={{
                opacity: [0.2, 0.5, 0.2],
                scale: [0.95, 1.08, 0.95],
              }}
              transition={{
                duration: 4,
                repeat: Infinity,
                ease: "easeInOut",
                delay: index * 0.3
              }}
            >
              {part}
            </motion.span> */}

              {/* Floating magical sparkles around longer words */}
              {/* {part.length > 4 && (
              <>
                <motion.div
                  className="absolute -top-3 -right-2 text-yellow-400 text-sm pointer-events-none"
                  animate={{
                    rotate: [0, 360],
                    scale: [0.8, 1.3, 0.8],
                    opacity: [0.4, 1, 0.4],
                  }}
                  transition={{
                    duration: 2.5 + index * 0.3,
                    repeat: Infinity,
                    ease: "easeInOut",
                    delay: 1 + index * 0.2
                  }}
                >
                  ✨
                </motion.div>
                
                {part.length > 6 && (
                  <motion.div
                    className="absolute -bottom-2 -left-1 text-pink-400 text-xs pointer-events-none"
                    animate={{
                      rotate: [360, 0],
                      scale: [0.6, 1.1, 0.6],
                      opacity: [0.3, 0.8, 0.3],
                    }}
                    transition={{
                      duration: 3 + index * 0.2,
                      repeat: Infinity,
                      ease: "easeInOut",
                      delay: 1.5 + index * 0.3
                    }}
                  >
                    ⭐
                  </motion.div>
                )}
              </>
            )} */}

              {/* Word emphasis effect for important words */}
              {/* {part.length > 5 && (
              <motion.div
                className="absolute inset-0 rounded-lg -z-5"
                animate={{
                  borderColor: [
                    "rgba(147, 51, 234, 0.1)",
                    "rgba(147, 51, 234, 0.4)",
                    "rgba(147, 51, 234, 0.1)"
                  ],
                  scale: [1, 1.02, 1],
                }}
                transition={{
                  duration: 3.5,
                  repeat: Infinity,
                  ease: "easeInOut",
                  delay: index * 0.4
                }}
              />
            )} */}
            </motion.span>

            {/* Enhanced Magical Drop Zones */}
            {index < parts.length - 1 && (
              <motion.div
                initial={{ scale: 0.8, opacity: 0 }}
                animate={{ scale: 1, opacity: 1 }}
                transition={{
                  duration: 0.4,
                  delay: 0.5 + index * 0.1,
                  ease: 'easeOut',
                }}
                className="inline-block relative"
                style={{ willChange: 'transform, opacity' }}
              >
                {/* Simplified magical aura around drop zone */}
                <motion.div
                  className="absolute -inset-2 bg-gradient-to-r from-purple-400/20 via-pink-400/20 to-indigo-400/20 rounded-xl blur-md -z-10"
                  animate={{
                    opacity: [0.2, 0.5, 0.2],
                  }}
                  transition={{
                    duration: 4,
                    repeat: Infinity,
                    ease: 'linear',
                    delay: index * 0.5,
                  }}
                  style={{ willChange: 'opacity' }}
                />

                {/* Simplified sparkles around drop zone */}
                {[...Array(1)].map((_, sparkleIndex) => (
                  <motion.div
                    key={sparkleIndex}
                    className="absolute w-1 h-1 bg-yellow-400 rounded-full pointer-events-none"
                    animate={{
                      opacity: [0.3, 0.8, 0.3],
                    }}
                    transition={{
                      duration: 3,
                      repeat: Infinity,
                      ease: 'linear',
                      delay: index * 0.5,
                    }}
                    style={{
                      left: '50%',
                      top: '50%',
                      willChange: 'opacity',
                    }}
                  />
                ))}

                <DroppableBlank
                  id={`blank-${index}`}
                  value={blanks[index]}
                  index={index}
                  onReset={handleResetBlank}
                  correctAnswers={currentQuestion.correct_answers}
                />
              </motion.div>
            )}
          </React.Fragment>
        ))}
      </div>
    </div>
  );
};

export default MagicalSentenceDisplay;
