'use client';
import React, { useState, useEffect, useRef } from 'react';
import { FiSearch, FiChevronDown } from 'react-icons/fi';
import { usePathname, useRouter } from 'next/navigation';
import Button, { ButtonIcon } from '@/components/Button';
import Modal from '@/components/Modal';

const Topbar = ({
  title = "Today's Diary",
  onSearch,
  onSortChange,
  onBackgroundChange,
  showContainer = false,
  searchResults = null,
  searchLoading = false,
  isSearching = false,
  onClearSearch,
  showSidebar = false,
  setShowSidebar,
}) => {
  const router = useRouter();
  const pathname = usePathname();
  const [isSearchTypeDropdownOpen, setIsSearchTypeDropdownOpen] =
    useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedSearchType, setSelectedSearchType] = useState('subject');
  const [isColorModalOpen, setIsColorModalOpen] = useState(false);
  const [selectedColor, setSelectedColor] = useState(null);
  const dropdownRef = useRef(null);

  // Search type options
  const searchTypes = [
    { value: 'subject', label: 'Search by Subject' },
    { value: 'date', label: 'Search by Date' },
  ];

  useEffect(() => {
    const handleClickOutside = (event) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        setIsSearchTypeDropdownOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  const handleSearchChange = (e) => {
    setSearchQuery(e.target.value);
  };

  const handleSearchSubmit = (e) => {
    e.preventDefault();
    if (onSearch && searchQuery.trim()) {
      // Create search parameters object based on selected search type
      const searchParams = {};

      if (selectedSearchType === 'subject') {
        searchParams.subject = searchQuery.trim();
      } else if (selectedSearchType === 'date') {
        searchParams.date = searchQuery.trim();
      }

      onSearch(searchParams);
    }
  };

  const handleSearchTypeSelect = (searchType) => {
    setSelectedSearchType(searchType.value);
    setIsSearchTypeDropdownOpen(false);
    // Clear search query when changing search type
    setSearchQuery('');
  };

  const handleSortClick = () => {
    if (onSortChange) {
      onSortChange();
    }
  };

  const handleBackgroundChange = () => {
    setIsColorModalOpen(true);
  };

  const handleCloseColorModal = () => {
    setIsColorModalOpen(false);
  };

  const handleApplyColor = (color) => {
    setSelectedColor(color);
    if (onBackgroundChange) {
      onBackgroundChange(color);
    }
    setIsColorModalOpen(false);
  };

  // Color options for the modal
  const colorOptions = [
    // First row
    { id: 'white', color: '#FFFFFF', borderColor: '#00A67E' },
    { id: 'lightBlue', color: '#E6F7FF' },
    { id: 'lightPink', color: '#FFE6E6' },
    { id: 'yellow', color: '#FFEB3B' },
    { id: 'lightYellow', color: '#FFF9C4' },
    // Second row
    { id: 'red', color: '#D32F2F' },
    { id: 'darkBlue', color: '#1A2A3A' },
    { id: 'orange', color: '#B45309' },
  ];

  const isDiaryPage =
    pathname === '/diary' ||
    pathname === '/diary/my' ||
    pathname.startsWith('/diary/my/item');

  return (
    <>
      <div
        className={`${
          pathname.startsWith('/diary')
            ? ' xl:w-[calc(100vw-256px)]'
            : ' xl:left-1/2 xl:-translate-x-1/2'
        } w-full fixed z-40 bg-[#D6FFF8] min-h-[68px] text-[#8B4513] py-3 max-sm:py-1 px-6 max-sm:px-3 flex items-center justify-between ${
          (showContainer ? 'max-w-7xl mx-auto px-5 xl:px-0' : 'shadow-md') 
        }`}
      >
        {/* Left side - Title */}
        <div className="sm:text-xl font-medium tracking-wide text-start">
          {title}
        </div>

        {/* Right side - Search and buttons */}
        {isDiaryPage && (
          <div className="flex items-center space-x-2">
            {/* <button className="px-5 py-2 bg-yellow-300 text-gray-800 rounded text-sm font-semibold border-2 border-yellow-300 hover:from-yellow-500 hover:to-yellow-700 transition-colors">
          New Diary
        </button> */}
            <div className="hidden block">
              <ButtonIcon icon={'mdi:search'} innerBtnCls={'h-12'} />
            </div>

            {/* Search type dropdown and search input */}
            <div
              className="relative lg:flex items-center hidden"
              ref={dropdownRef}
            >
              <button
                className="bg-[#FFDE34] text-black px-3 py-2.5 rounded-l-md flex items-center justify-between min-w-[140px] topbar-dropdown"
                onClick={() =>
                  setIsSearchTypeDropdownOpen(!isSearchTypeDropdownOpen)
                }
              >
                <span className="text-sm font-medium truncate">
                  {searchTypes.find((type) => type.value === selectedSearchType)
                    ?.label || 'Search by Subject'}
                </span>
                <FiChevronDown
                  className={`ml-1 transition-transform duration-200 ${
                    isSearchTypeDropdownOpen ? 'transform rotate-180' : ''
                  }`}
                  size={16}
                />
              </button>

              {isSearchTypeDropdownOpen && (
                <div className="absolute top-full left-0 mt-1 w-full bg-white rounded-md shadow-lg z-10 max-h-60 overflow-y-auto">
                  {searchTypes.map((searchType, index) => (
                    <div
                      key={index}
                      className="px-3 py-2 text-sm text-gray-700 hover:bg-gray-100 cursor-pointer"
                      onClick={() => handleSearchTypeSelect(searchType)}
                    >
                      {searchType.label}
                    </div>
                  ))}
                </div>
              )}

              <form onSubmit={handleSearchSubmit} className="flex">
                <input
                  type={selectedSearchType === 'date' ? 'date' : 'text'}
                  placeholder={
                    selectedSearchType === 'date'
                      ? 'Select date'
                      : 'Search by subject'
                  }
                  className="border-0 px-3 py-2 w-48 focus:outline-none text-black search-input"
                  value={searchQuery}
                  onChange={handleSearchChange}
                />
                <button
                  type="submit"
                  className="bg-white text-gray-600 px-2 py-2 rounded-r-md hover:text-gray-800 topbar-button"
                >
                  <FiSearch size={18} />
                </button>
              </form>

              {/* Search Results Dropdown */}
              {isSearching && (
                <div className="absolute top-full left-0 right-0 mt-1 bg-white rounded-md shadow-lg border border-gray-200 z-50 max-h-96 overflow-hidden">
                  {/* Header */}
                  <div className="flex items-center justify-between p-3 border-b border-gray-200 bg-gray-50">
                    <span className="text-sm font-medium text-gray-900">
                      {searchLoading
                        ? 'Searching...'
                        : `Search Results (${
                            searchResults?.totalItems || 0
                          } found)`}
                    </span>
                    <button
                      onClick={onClearSearch}
                      className="text-xs text-gray-500 hover:text-gray-700 underline"
                    >
                      Clear
                    </button>
                  </div>

                  {/* Content */}
                  <div className="overflow-y-auto max-h-80">
                    {searchLoading ? (
                      <div className="text-center py-8">
                        <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-amber-500 mx-auto"></div>
                        <p className="text-gray-500 mt-2 text-sm">
                          Searching diary entries...
                        </p>
                      </div>
                    ) : searchResults?.items?.length > 0 ? (
                      <div>
                        {searchResults.items.map((entry) => (
                          <div
                            key={entry.id}
                            className="p-3 hover:bg-gray-50 cursor-pointer transition-colors border-b border-gray-100 last:border-b-0"
                            onClick={() => {
                              console.log('Selected diary entry:', entry);
                              // Navigate to diary route with entryId parameter
                              router.push(`/diary?entryId=${entry.id}`);
                              onClearSearch?.(); // Close dropdown when item is selected
                            }}
                          >
                            <div className="flex items-start justify-between">
                              <div className="flex-1 min-w-0">
                                <h4 className="font-medium text-gray-900 truncate text-sm mb-1">
                                  {entry.title}
                                </h4>
                                <p className="text-xs text-gray-600 mb-2 line-clamp-2">
                                  {entry.content?.substring(0, 80)}...
                                </p>
                                <div className="flex items-center text-xs text-gray-500 space-x-2">
                                  <span>
                                    {new Date(
                                      entry.entryDate
                                    ).toLocaleDateString()}
                                  </span>
                                  <span>•</span>
                                  <span
                                    className={`px-1.5 py-0.5 rounded-full text-xs ${
                                      entry.status === 'reviewed'
                                        ? 'bg-green-100 text-green-800'
                                        : entry.status === 'pending'
                                        ? 'bg-yellow-100 text-yellow-800'
                                        : 'bg-gray-100 text-gray-800'
                                    }`}
                                  >
                                    {entry.status}
                                  </span>
                                  <span>•</span>
                                  <span>Score: {entry.score || 'N/A'}</span>
                                </div>
                              </div>
                              <div className="ml-3 text-xs text-gray-400 flex-shrink-0">
                                {entry.diary?.userName}
                              </div>
                            </div>
                          </div>
                        ))}
                      </div>
                    ) : (
                      searchResults && (
                        <div className="text-center py-8 text-gray-500">
                          <svg
                            className="w-12 h-12 text-gray-300 mx-auto mb-3"
                            fill="none"
                            stroke="currentColor"
                            viewBox="0 0 24 24"
                          >
                            <path
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              strokeWidth={1}
                              d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                            />
                          </svg>
                          <p className="text-sm font-medium mb-1">
                            No diary entries found
                          </p>
                          <p className="text-xs">
                            Try adjusting your search criteria
                          </p>
                        </div>
                      )
                    )}
                  </div>
                </div>
              )}
            </div>

            {/* Change Background button */}
            {
              <Button
                icon="mdi:palette-outline"
                buttonText="Change Background"
                onClick={handleBackgroundChange}
                className="px-3 py-1.5 w-auto"
              />
            }
          </div>
        )}

        {/* Color Picker Modal */}
        <Modal
          isOpen={isColorModalOpen}
          onClose={handleCloseColorModal}
          position="center"
          title="Change Background Color"
          width="md"
        >
          <div className="py-4">
            {/* First row - 5 colors */}
            <div className="grid grid-cols-5 gap-6 mb-8">
              {colorOptions.slice(0, 5).map((option) => (
                <div key={option.id} className="flex justify-center">
                  <div className="w-16 h-16 bg-white rounded-md shadow-md flex items-center justify-center cursor-pointer">
                    <button
                      className={`w-10 h-10 rounded-full shadow-sm flex items-center justify-center ${
                        selectedColor === option.color
                          ? 'ring-2 ring-offset-2 ring-yellow-500'
                          : ''
                      }`}
                      style={{
                        backgroundColor: option.color,
                        border: option.borderColor
                          ? `2px solid ${option.borderColor}`
                          : 'none',
                      }}
                      onClick={() => setSelectedColor(option.color)}
                      onDoubleClick={() => handleApplyColor(option.color)}
                      aria-label={`Select ${option.id} color`}
                    >
                      {selectedColor === option.color && (
                        <span
                          className={`${
                            option.id === 'white' ||
                            option.id === 'lightYellow' ||
                            option.id === 'lightBlue' ||
                            option.id === 'lightPink'
                              ? 'text-black'
                              : 'text-white'
                          } text-lg`}
                        >
                          ✓
                        </span>
                      )}
                    </button>
                  </div>
                </div>
              ))}
            </div>

            {/* Second row - 3 colors */}
            <div className="grid grid-cols-5 gap-6 mb-10">
              <div className="col-span-1"></div>
              {colorOptions.slice(5).map((option) => (
                <div key={option.id} className="flex justify-center">
                  <div className="w-16 h-16 bg-white rounded-md shadow-md flex items-center justify-center cursor-pointer">
                    <button
                      className={`w-10 h-10 rounded-full shadow-sm flex items-center justify-center ${
                        selectedColor === option.color
                          ? 'ring-2 ring-offset-2 ring-yellow-500'
                          : ''
                      }`}
                      style={{
                        backgroundColor: option.color,
                        border: option.borderColor
                          ? `2px solid ${option.borderColor}`
                          : 'none',
                      }}
                      onClick={() => setSelectedColor(option.color)}
                      onDoubleClick={() => handleApplyColor(option.color)}
                      aria-label={`Select ${option.id} color`}
                    >
                      {selectedColor === option.color && (
                        <span className="text-white text-lg">✓</span>
                      )}
                    </button>
                  </div>
                </div>
              ))}
              <div className="col-span-1"></div>
            </div>

            <div className="flex justify-center space-x-6">
              <button
                className="px-10 py-3 bg-[#FFFEF0] text-[#8B4513] rounded-full border-2 border-[#8B4513] font-medium hover:bg-[#FFF9C4] transition-colors shadow-md"
                onClick={handleCloseColorModal}
              >
                Cancel
              </button>

              <button
                className="px-10 py-3 bg-gradient-to-b from-yellow-400 to-yellow-600 text-black rounded-full border-2 border-yellow-300 font-medium shadow-md hover:from-yellow-500 hover:to-yellow-700 transition-colors"
                onClick={() => handleApplyColor(selectedColor)}
                disabled={!selectedColor}
              >
                Apply
              </button>
            </div>
          </div>
        </Modal>
      </div>

      {!pathname.startsWith('/diary') && (
        <div className="fixed z-20 w-full h-[55px] sm:h-[68px] bg-[#D6FFF8]"></div>
      )}
      <div className="h-[55px] sm:h-[68px] bg-[#D6FFF8]"></div>
    </>
  );
};

export default Topbar;
