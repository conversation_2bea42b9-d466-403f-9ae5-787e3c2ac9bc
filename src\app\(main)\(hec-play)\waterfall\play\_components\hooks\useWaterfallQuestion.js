import { useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import {
  setBlanks,
  setOptions,
  updateBlank,
  resetBlank,
  updateOptionUsed,
  setShowFeedback,
  setIsCorrect,
  setHasChecked,
  initializeQuestion,
  addUserAnswer,
  incrementScore,
  selectCurrentQuestionIndex,
  selectQuestions,
  selectBlanks,
  selectOptions,
  selectShowFeedback,
  selectIsCorrect,
  selectHasChecked,
  selectCurrentQuestion,
} from '@/store/features/waterfallSlice';
import { createAnswerData } from '../utils/answerUtils';

export const useWaterfallQuestion = () => {
  const dispatch = useDispatch();
  
  const currentQuestionIndex = useSelector(selectCurrentQuestionIndex);
  const questions = useSelector(selectQuestions);
  const blanks = useSelector(selectBlanks);
  const options = useSelector(selectOptions);
  const showFeedback = useSelector(selectShowFeedback);
  const isCorrect = useSelector(selectIsCorrect);
  const hasChecked = useSelector(selectHasChecked);
  const currentQuestion = useSelector(selectCurrentQuestion);

  // Initialize blanks and options for the current question
  useEffect(() => {
    if (questions.length > 0 && currentQuestionIndex < questions.length) {
      const question = questions[currentQuestionIndex];

      // Count the number of blanks in the question
      const blankCount = (
        question.question_text_plain.match(/\[\[gap\]\]/g) || []
      ).length;

      // Initialize options array with the provided options and correct answers
      const allOptions = [...question.options];

      // Add correct answers if they're not already in the options
      if (question.correct_answers) {
        question.correct_answers.forEach(correctAnswer => {
          if (!allOptions.includes(correctAnswer)) {
            allOptions.push(correctAnswer);
          }
        });
      }

      // Shuffle the options array to randomize order and improve user experience
      const shuffledOptions = allOptions.sort(() => Math.random() - 0.5);

      const formattedOptions = shuffledOptions.map((option, index) => ({
        id: `option-${index}`,
        text: option,
        used: false,
      }));

      // Initialize question state
      dispatch(initializeQuestion({
        question,
        blankCount,
        shuffledOptions: formattedOptions
      }));
    }
  }, [questions, currentQuestionIndex, dispatch]);

  // Reset current question - used by modals
  const resetCurrentQuestion = () => {
    if (!currentQuestion) return;

    const blankCount = (
      currentQuestion.question_text_plain.match(/\[\[gap\]\]/g) || []
    ).length;
    
    dispatch(setBlanks(Array(blankCount).fill(null)));
    
    // Combine question options with correct answers to create complete options array
    const allOptions = [...currentQuestion.options];

    // Add correct answers if they're not already in the options
    if (currentQuestion.correct_answers) {
      currentQuestion.correct_answers.forEach(correctAnswer => {
        if (!allOptions.includes(correctAnswer)) {
          allOptions.push(correctAnswer);
        }
      });
    }

    // Shuffle the options array to randomize order and improve user experience
    const shuffledOptions = allOptions.sort(() => Math.random() - 0.5);

    dispatch(setOptions(
      shuffledOptions.map((option, index) => ({
        id: `option-${index}`,
        text: option,
        used: false,
      }))
    ));

    // Reset question state
    dispatch(setShowFeedback(false));
    dispatch(setIsCorrect(false));
    dispatch(setHasChecked(false));
  };

  // Handle dropping an option into a blank
  const handleDropOption = (blankIndex, optionText, optionId) => {
    // Update the blank with the dropped option
    dispatch(updateBlank({ index: blankIndex, value: optionText }));
    
    // Mark the option as used
    dispatch(updateOptionUsed({ optionId, used: true }));

    // Reset feedback when making changes
    if (hasChecked) {
      dispatch(setShowFeedback(false));
      dispatch(setHasChecked(false));
    }
  };

  // Reset a single blank and return its option to the available options
  const handleResetBlank = (index) => {
    const blankValue = blanks[index];

    if (blankValue) {
      // Reset the blank
      dispatch(resetBlank(index));

      // Find the option that matches this value and mark it as unused
      const option = options.find(opt => opt.text === blankValue);
      if (option) {
        dispatch(updateOptionUsed({ optionId: option.id, used: false }));
      }

      // Reset feedback when making changes
      if (hasChecked) {
        dispatch(setShowFeedback(false));
        dispatch(setHasChecked(false));
      }
    }
  };

  // Check answers for the current question
  const handleCheckAnswers = () => {
    if (!currentQuestion) return;

    // Check if all blanks are filled
    const hasEmptyBlanks = blanks.some(blank => blank === null || blank === '');
    if (hasEmptyBlanks) {
      return { error: 'Please fill in all blanks before checking.' };
    }

    // Create answer data and save it
    const answerData = createAnswerData(currentQuestion, blanks);
    if (answerData) {
      dispatch(setIsCorrect(answerData.is_correct));
      dispatch(setShowFeedback(true));
      dispatch(setHasChecked(true));

      // Add to user answers
      dispatch(addUserAnswer(answerData));

      // Award points if correct
      if (answerData.is_correct) {
        dispatch(incrementScore(10));
      }

      return { success: true, isCorrect: answerData.is_correct };
    }

    return { error: 'Failed to check answers.' };
  };

  // Get correct answer for display
  const getCorrectAnswer = () => {
    if (!currentQuestion) return '';

    let gapIndex = 0;
    return currentQuestion.question_text_plain.replace(
      /\[\[gap\]\]/g,
      () => {
        const correctAnswer = currentQuestion.correct_answers[gapIndex];
        gapIndex++;
        return correctAnswer;
      }
    );
  };

  // Generate parts for MagicalSentenceDisplay
  const getQuestionParts = () => {
    if (!currentQuestion) return [];
    return currentQuestion.question_text_plain.split(/\[\[gap\]\]/g);
  };

  return {
    // State
    currentQuestion,
    blanks,
    options,
    showFeedback,
    isCorrect,
    hasChecked,
    currentQuestionIndex,

    // Actions
    handleDropOption,
    handleResetBlank,
    handleCheckAnswers,
    resetCurrentQuestion,

    // Utils
    getCorrectAnswer,
    getQuestionParts,
    hasEmptyBlanks: blanks.some(blank => blank === null || blank === ''),
    isQuestionComplete: blanks.every(blank => blank !== null && blank !== ''),
  };
};
