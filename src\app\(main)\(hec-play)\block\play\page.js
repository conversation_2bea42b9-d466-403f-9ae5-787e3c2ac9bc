'use client';
import React, { useEffect, useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useRouter } from 'next/navigation';
import { useDispatch } from 'react-redux';
import api from '@/lib/api';
import SentenceBlock from './components/SentenceBlock';
import GameInstructionView from './components/GameInstructionView';
import InitialInstructionsStage from './components/InitialInstructionsStage';
import GameResultsStage from './components/GameResultsStage';
import Modal from '@/components/Modal';
import BlockPlayModal from './components/BlockPlayModal';
import GoBack from '@/components/shared/GoBack';
import { resetQuestionState } from '@/store/features/hecPlaySlice';
// import AchievementNotification from './components/AchievementNotification';

// Game stages enum
const GAME_STAGES = {
  // INITIAL_INSTRUCTIONS: 'initial_instructions',
  SENTENCE_BUILDING: 'sentence_building',
  WORD_EXPANSION: 'word_expansion',
  RESULTS: 'results',
};

const BlockSentenceBuilder = () => {
  const router = useRouter();
  const dispatch = useDispatch();
  const [gameData, setGameData] = useState(null);
  const [startingSentences, setStartingSentences] = useState([]);
  const [expandingSentences, setExpandingSentences] = useState([]);
  const [draggedWord, setDraggedWord] = useState(null);
  const [usedWords, setUsedWords] = useState([]);
  const [currentStep, setCurrentStep] = useState(0);
  const [currentStage, setCurrentStage] = useState(
    GAME_STAGES.SENTENCE_BUILDING
  );
  const [gameResults, setGameResults] = useState(null);
  const [showAchievement, setShowAchievement] = useState(false);
  const [achievementData, setAchievementData] = useState(null);
  const [isGameStarted, setIsGameStarted] = useState(false);
  const [modalStage, setModalStage] = useState('sentence_building'); // 'sentence_building' or 'word_expansion'

  useEffect(() => {
    const fetchData = async () => {
      try {
        const res = await api.get('/play/block/play');
        const sentences = res.data.sentences;
        const start = sentences.map((s) =>
          Array(s.starting_part.split(' ').length).fill(null)
        );
        const expand = sentences.map((s) =>
          Array(s.expanding_part.split(' ').length).fill(null)
        );
        setGameData(res.data);
        setStartingSentences(start);
        setExpandingSentences(expand);
      } catch (err) {
        console.error('Error fetching block data:', err);
      }
    };
    fetchData();
  }, []);

  const handleDragStart = (word, type, wordIndex) => {
    setDraggedWord({ word, type, wordIndex });
  };

  const handleDrop = (type, index, blockIndex) => {
    if (!draggedWord || draggedWord.type !== type) return;
    if (
      type === 'expand' &&
      startingSentences[blockIndex].some((w) => w === null)
    )
      return;
    const sentences =
      type === 'start' ? [...startingSentences] : [...expandingSentences];
    const current = sentences[blockIndex];

    // If there's already a word in this position, find its index and remove it from usedWords first
    if (current[index]) {
      // Find the index of the word being replaced
      const wordToReplace = current[index];
      const wordsList = type === 'start' ? gameData.words : gameData.expansion_words;
      const wordIndexToReplace = wordsList.findIndex(w => w === wordToReplace);
      if (wordIndexToReplace !== -1) {
        setUsedWords((prev) => prev.filter((idx) => idx !== wordIndexToReplace));
      }
    }

    // Place the new word
    current[index] = draggedWord.word;
    if (type === 'start') setStartingSentences(sentences);
    else setExpandingSentences(sentences);

    // Add the new word index to usedWords (avoid duplicates)
    setUsedWords((prev) => {
      if (!prev.includes(draggedWord.wordIndex)) {
        return [...prev, draggedWord.wordIndex];
      }
      return prev;
    });
    setDraggedWord(null);
  };

  const removeWord = (index, type, blockIndex) => {
    const sentences =
      type === 'start' ? [...startingSentences] : [...expandingSentences];
    const current = sentences[blockIndex];
    const wordToRemove = current[index];

    if (wordToRemove) {
      current[index] = null;
      if (type === 'start') setStartingSentences(sentences);
      else setExpandingSentences(sentences);

      // Remove the word index from usedWords to make it available again
      const wordsList = type === 'start'
        ? gameData.word_blocks.starting_words
        : gameData.word_blocks.expanding_words;
      const wordIndexToRemove = wordsList.findIndex(w => w === wordToRemove);
      if (wordIndexToRemove !== -1) {
        setUsedWords((prev) => prev.filter((idx) => idx !== wordIndexToRemove));
      }
    }
  };

  const allComplete = startingSentences.every(
    (s, i) =>
      s.every((w) => w !== null) &&
      expandingSentences[i].every((w) => w !== null)
  );

  const allSentencesBuilt = startingSentences.every((s) =>
    s.every((w) => w !== null)
  );

  const openGameModal = () => {
    setIsGameStarted(true);
    setModalStage('sentence_building');
  };

  const startWordExpansion = () => {
    setModalStage('word_expansion');
    // Reset usedWords when transitioning to expanding phase since we're using different word arrays
    setUsedWords([]);
  };

  const submitAll = async () => {
    try {
      // Prepare sentence constructions from the completed sentences
      const sentenceConstructions = gameData.sentences.map((_, index) => {
        const startingSentence =
          startingSentences[index]?.filter((word) => word !== null).join(' ') ||
          '';
        const expandingSentence =
          expandingSentences[index]
            ?.filter((word) => word !== null)
            .join(' ') || '';

        return {
          starting_sentence: startingSentence,
          expanding_sentence: expandingSentence,
          sentence_order: index + 1,
        };
      });

      const payload = {
        block_game_id: gameData.id,
        sentence_constructions: sentenceConstructions,
      };

      console.log('Submitting payload:', payload);

      const response = await api.post('/play/block/submit', payload);
      console.log('Game submitted successfully:', response.data);

      // Store results and transition to results stage
      setGameResults(response.data);
      setCurrentStage(GAME_STAGES.RESULTS);
      setIsGameStarted(false); // Close the modal
    } catch (error) {
      console.error('Error submitting game:', error);
    }
  };

  const restartGame = () => {
    // Reset all game state
    setCurrentStep(0);
    setCurrentStage(GAME_STAGES.SENTENCE_BUILDING);
    setGameResults(null);
    setUsedWords([]);
    setIsGameStarted(false);
    setModalStage('sentence_building');

    // Reset sentences
    if (gameData) {
      const start = gameData.sentences.map((s) =>
        Array(s.starting_part.split(' ').length).fill(null)
      );
      const expand = gameData.sentences.map((s) =>
        Array(s.expanding_part.split(' ').length).fill(null)
      );
      setStartingSentences(start);
      setExpandingSentences(expand);
    }
  };

  const exitGame = () => {
    // Clear all game states
    setCurrentStep(0);
    setCurrentStage(GAME_STAGES.SENTENCE_BUILDING);
    setGameResults(null);
    setUsedWords([]);
    setIsGameStarted(false);
    setModalStage('sentence_building');
    setDraggedWord(null);

    // Clear Redux state
    dispatch(resetQuestionState());

    // Navigate to block page
    router.push('/block');
  };

  if (!gameData) return <div className="p-6 text-center">Loading...</div>;

  const renderCurrentStage = () => {
    switch (currentStage) {
      // case GAME_STAGES.INITIAL_INSTRUCTIONS:
      //   return (
      //     <InitialInstructionsStage
      //       gameData={gameData}
      //       onStartGame={startGame}
      //     />
      //   );

      case GAME_STAGES.SENTENCE_BUILDING:
        return (
          <div className="w-full space-y-6 sm:space-y-8">
            <div className="w-full max-w-7xl mx-auto">
              <GoBack
                title={'HEC Block Play'}
                linkClass="mb-5 w-full max-w-52"
              />
              <GameInstructionView gameData={gameData} />
            </div>
            <div className="text-center">
              <button
                onClick={openGameModal}
                className="bg-gradient-to-b from-yellow-500 to-yellow-400 hover:from-yellow-500 hover:to-yellow-500 text-white font-bold py-3 px-8 sm:py-4 sm:px-12 rounded-full text-lg sm:text-xl transition-all duration-300 flex items-center gap-3 mx-auto"
              >
                Let's Play
              </button>
            </div>

            {/* Unified Game Modal */}
            {isGameStarted && (
              <BlockPlayModal
                isOpen={isGameStarted}
                onClose={() => {
                  setIsGameStarted(false);
                  setModalStage('sentence_building');
                }}
                currentStep={
                  modalStage === 'sentence_building' ? currentStep : 0
                }
                totalSteps={gameData.sentences.length * 2} // Total steps = starting + expanding sentences
                completedSteps={
                  // Calculate overall progress: completed starting sentences + completed expanding sentences
                  startingSentences.filter((s) => s.every((w) => w !== null))
                    .length +
                  expandingSentences.filter((s) => s.every((w) => w !== null))
                    .length
                }
                stageTitle={
                  modalStage === 'sentence_building'
                    ? 'Building Sentences'
                    : 'Expanding Words'
                }
                showProgress={true}
              >
                {modalStage === 'sentence_building' ? (
                  <SentenceBlock
                    type="start"
                    show={true}
                    hide={false}
                    currentStep={currentStep}
                    gameData={gameData}
                    startingSentences={startingSentences}
                    expandingSentences={expandingSentences}
                    usedWords={usedWords}
                    handleDragStart={handleDragStart}
                    handleDrop={handleDrop}
                    removeWord={removeWord}
                    onNext={() => {
                      if (currentStep + 1 >= gameData.sentences.length) {
                        startWordExpansion();
                      } else {
                        setCurrentStep((prev) => prev + 1);
                      }
                    }}
                    allSentencesBuilt={allSentencesBuilt}
                    onStartExpansion={startWordExpansion}
                    onExit={exitGame}
                  />
                ) : (
                  <SentenceBlock
                    type="expand"
                    show={true}
                    hide={false}
                    currentStep={currentStep}
                    gameData={gameData}
                    startingSentences={startingSentences}
                    expandingSentences={expandingSentences}
                    usedWords={usedWords}
                    handleDragStart={handleDragStart}
                    handleDrop={handleDrop}
                    removeWord={removeWord}
                    allComplete={allComplete}
                    onSubmit={submitAll}
                    showCompletedSentences={true}
                    onExit={exitGame}
                  />
                )}
              </BlockPlayModal>
            )}
          </div>
        );

      case GAME_STAGES.WORD_EXPANSION:
        // This stage is now handled within the unified modal above
        return (
          <div className="w-full space-y-6 sm:space-y-8">
            <div className="w-full max-w-5xl mx-auto">
              <GameInstructionView gameData={gameData} />
            </div>
            <div className="text-center">
              <p className="text-lg text-gray-600">
                Word expansion is in progress...
              </p>
            </div>
          </div>
        );

      case GAME_STAGES.RESULTS:
        return (
          <GameResultsStage
            gameResults={gameResults}
            gameData={gameData}
            startingSentences={startingSentences}
            expandingSentences={expandingSentences}
            onRestart={restartGame}
          />
        );

      default:
        return null;
    }
  };

  return (
    <div className="py-4 sm:py-6 lg:py-10 px-4 sm:px-6 lg:px-8 flex flex-col items-center text-[#5A3D1A] bg-white">
      <div className="w-full max-w-7xl mx-auto">
        <AnimatePresence mode="wait">
          <motion.div
            key={currentStage}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            transition={{ duration: 0.5 }}
            className="w-full flex flex-col items-center"
          >
            {renderCurrentStage()}
          </motion.div>
        </AnimatePresence>
      </div>
    </div>
  );
};

export default BlockSentenceBuilder;
