import { ButtonIcon } from '@/components/Button';
import GoBack from '@/components/shared/GoBack';
import { Icon } from '@iconify/react';
import Image from 'next/image';
import Link from 'next/link';
import React from 'react';

const Block = () => {
  const stageInstructions = [
    {
      stage: 1,
      title: 'Sentence Building Phase',
      description: `You'll build starting sentences using the words from Block Set #1`,
      icon: 'mdi:text-box-plus',
      color: 'text-blue-600',
      bgColor: 'bg-blue-50',
    },
    {
      stage: 2,
      title: 'Word Expansion Phase',
      description:
        'Expand each sentence using additional words from Block Set #2',
      icon: 'mdi:text-box-multiple',
      color: 'text-green-600',
      bgColor: 'bg-green-50',
    },
    {
      stage: 3,
      title: 'Results & Scoring',
      description: `See your completed sentences and earn your points!`,
      icon: 'mdi:trophy',
      color: 'text-yellow-600',
      bgColor: 'bg-yellow-50',
    },
  ];

  return (
    <div className="max-w-7xl mx-auto p-5 pt-0 xl:px-0">
      <GoBack title={'HEC Block Play'} linkClass="my-5 w-full max-w-52" />

      <div className="p-5 xl:py-10 bg-yellow-100 border w-full rounded-lg shadow-lg ">
        <div className="max-w-5xl mx-auto mb-3 flex max-sm:flex-col items-center justify-between text-center gap-5">
          <div className="space-y-3">
            <Image
              src={'/assets/images/all-img/block.png'}
              alt={'waterfall'}
              width={400}
              height={500}
              className="max-w-[450px] max-sm:max-w-[250px] h-auto mx-auto"
            />
            <h1 className="text-3xl sm:text-5xl font-semibold bg-gradient-to-b from-gray-500 to-gray-900 text-transparent bg-clip-text">
              BLOCK PLAY
            </h1>
          </div>

          <Link
            href={'/block/play'}
            className="flex items-center gap-2 border border-yellow-800 text-xl sm:text-xl lg:text-3xl text-yellow-900 bg-gradient-to-b from-[#DCA600] to-[#FFDE5B] px-10 py-1 rounded-full mt-3"
          >
            Let's Play{' '}
            <ButtonIcon
              icon={'famicons:play'}
              innerBtnCls={'h-10 lg:h-14 w-10 lg:w-14'}
              btnIconCls={'h-4 w-4 lg:h-6 lg:w-6 text-white'}
            />
          </Link>
        </div>
      </div>

      {/* Decorative Elements */}
      <div
        // initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 1, delay: 1 }}
        className="my-8 text-center"
      >
        <Image
          src="/assets/Frame 1000007108.png"
          alt="Decoration"
          width={500}
          height={20}
          className="w-full h-auto opacity-60"
        />
      </div>

      <div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6, delay: 0.2 }}
        className="mb-8 max-w-4xl mx-auto"
      >
        <h2 className="text-2xl font-bold text-[#8B4513] text-center mb-8">
          How to Play
        </h2>

        <div className="space-y-6">
          {stageInstructions.map((stage, index) => (
            <div
              key={stage.stage}
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.5, delay: 0.3 + index * 0.1 }}
              className={`${stage.bgColor} rounded-2xl p-3 sm:p-6 border-2 border-opacity-20 border-gray-300`}
            >
              <div className="flex items-start gap-4">
                <div
                  className={`${stage.color} ${stage.bgColor} p-1.5 sm:p-3 rounded-full border-2 border-current border-opacity-20`}
                >
                  <Icon icon={stage.icon} className="w-6 h-6" />
                </div>
                <div className="flex-1">
                  <div className="flex max-sm:flex-col sm:items-center gap-2 mb-2">
                    <span className="bg-white max-sm:max-w-20 px-3 py-1 rounded-full text-sm font-bold text-[#8B4513]">
                      Stage {stage.stage}
                    </span>
                    <h3 className="text-lg font-bold text-[#8B4513]">
                      {stage.title}
                    </h3>
                  </div>
                  <p className="text-[#5A3D1A]">{stage.description}</p>
                </div>
              </div>
            </div>
          ))}

          {/* Game Tips */}
          <div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.5 }}
            className="bg-gradient-to-r from-orange-50 to-yellow-50 rounded-2xl p-6 mb-8 border-2 border-orange-200"
          >
            <div className="flex items-start gap-3">
              <Icon
                icon="mdi:lightbulb"
                className="w-6 h-6 text-orange-500 mt-1"
              />
              <div>
                <h3 className="font-bold text-[#8B4513] mb-2">Pro Tips:</h3>
                <ul className="text-[#5A3D1A] space-y-1 text-sm">
                  <li>
                    • Drag and drop words from the blocks to build your
                    sentences
                  </li>
                  <li>
                    • You can remove words by clicking on them in your sentence
                  </li>
                  <li>
                    • Complete all starting sentences before moving to expansion
                  </li>
                  <li>• Take your time - there's no time limit!</li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Block;
