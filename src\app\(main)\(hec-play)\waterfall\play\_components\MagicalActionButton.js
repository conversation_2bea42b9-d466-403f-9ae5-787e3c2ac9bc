'use client';
import React from 'react';
import { motion } from 'framer-motion';
import { Icon } from '@iconify/react';

const MagicalActionButton = ({
  onClick,
  isSubmitting,
  showTimeExpiredModal,
  isLastQuestion,
  hasChecked,
  onExit,
}) => {
  const isDisabled = isSubmitting || showTimeExpiredModal;

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5, delay: 0.8 }}
      className="flex flex-col max-sm:flex-col-reverse sm:flex-row justify-center items-center max-sm:gap-2 gap-4 mt-8"
    >
      {/* Exit Game Button */}
      <motion.button
        onClick={onExit}
        whileTap={{ scale: 0.95 }}
        className="relative overflow-hidden px-4 sm:px-6 py-3 rounded-xl font-bold text-sm sm:text-base flex items-center gap-2 transition-all duration-300 shadow-lg border-2 bg-gradient-to-r from-red-100 via-red-50 to-red-100 hover:from-red-200 hover:via-red-100 hover:to-red-200 text-red-700 border-red-300 hover:border-red-400"
        style={{
          boxShadow: '0 4px 15px rgba(239, 68, 68, 0.2)',
        }}
      >
        {/* Exit button sparkle effect */}
        <motion.div
          className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent"
          animate={{ x: [-100, 200] }}
          transition={{ duration: 3, repeat: Infinity, repeatDelay: 4 }}
        />

        <Icon icon="material-symbols:logout" className="text-lg" />
        <span className="relative z-10 font-bold"> Exit Game</span>

      </motion.button>
      <motion.button
        onClick={onClick}
        disabled={isDisabled}
        whileTap={!isDisabled ? { scale: 0.95 } : {}}
        className={`
          relative overflow-hidden px-5 sm:px-6 py-3 rounded-2xl font-bold sm:text-xl flex items-center gap-3 transition-all duration-300 shadow-xl border-2
          ${
            isDisabled
              ? 'bg-gray-300 text-gray-500 cursor-not-allowed border-gray-400'
              : 'bg-gradient-to-r from-yellow-400 via-orange-400 to-yellow-500 text-white hover:from-yellow-500 hover:via-orange-500 hover:to-yellow-600 border-yellow-300 hover:border-orange-300'
          }
        `}
        style={{
          background: !isDisabled
            ? `
            linear-gradient(135deg, #fbbf24 0%, #f97316 50%, #fbbf24 100%),
            radial-gradient(circle at 30% 30%, rgba(255,255,255,0.3) 0%, transparent 50%)
          `
            : undefined,
          boxShadow: !isDisabled
            ? '0 8px 25px rgba(251, 191, 36, 0.4)'
            : undefined,
        }}
      >
        {/* Enhanced sparkle effect */}
        {!isDisabled && (
          <>
            <motion.div
              className="absolute inset-0 bg-gradient-to-r from-transparent via-white/30 to-transparent"
              animate={{ x: [-150, 400] }}
              transition={{ duration: 2.5, repeat: Infinity, repeatDelay: 4 }}
            />

            {/* Floating sparkles around button */}
            {[...Array(4)].map((_, i) => (
              <motion.div
                key={i}
                className="absolute w-1 h-1 bg-white rounded-full pointer-events-none"
                animate={{
                  x: [0, 20, -15, 0],
                  y: [0, -15, 10, 0],
                  opacity: [0.4, 1, 0.4],
                  scale: [1, 1.5, 1],
                }}
                transition={{
                  duration: 2 + i * 0.3,
                  repeat: Infinity,
                  delay: i * 0.5,
                  ease: 'easeInOut',
                }}
                style={{
                  left: `${15 + i * 20}%`,
                  top: `${20 + (i % 2) * 60}%`,
                }}
              />
            ))}
          </>
        )}

        {/* Enhanced text with magical styling */}
        <span className="relative z-10 font-bold text-lg tracking-wide">
          {isSubmitting
            ? '🔮 Processing...'
            : hasChecked
            ? isLastQuestion
              ? '🏆 Submit Game'
              : '🚀 Next Challenge'
            : 'Check Answer'}
        </span>

        {/* Animated arrow */}
        {!isSubmitting && (
          <motion.div
            animate={{ x: [0, 8, 0] }}
            transition={{ duration: 1.5, repeat: Infinity, ease: 'easeInOut' }}
            className="relative z-10"
          >
            <Icon
              icon="material-symbols:arrow-forward-rounded"
              className="text-2xl"
            />
          </motion.div>
        )}

        {/* Button magical aura */}
        {!isDisabled && (
          <motion.div
            className="absolute inset-0 rounded-2xl border-2 border-yellow-300/50 pointer-events-none"
            animate={{
              boxShadow: [
                '0 0 20px rgba(251, 191, 36, 0.3)',
                '0 0 40px rgba(251, 191, 36, 0.6)',
                '0 0 20px rgba(251, 191, 36, 0.3)',
              ],
            }}
            transition={{
              duration: 2.5,
              repeat: Infinity,
              ease: 'easeInOut',
            }}
          />
        )}
      </motion.button>
    </motion.div>
  );
};

export default MagicalActionButton;
