'use client';

import GoBack from '@/components/shared/GoBack';
import useDataFetch from '@/hooks/useDataFetch';
import api from '@/lib/api';
import { loginSuccess } from '@/store/features/authSlice';
import { Icon } from '@iconify/react';
import Image from 'next/image';
import { useRouter, useSearchParams } from 'next/navigation';
import React, { useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';

const OrderCheckout = () => {
  const router = useRouter();
  const dispatch = useDispatch();
  const planId = useSearchParams().get('planId');
  const checkoutType = useSearchParams().get('type');
  const [method, setMethod] = useState('kcp_card');
  const [autoRenew, setAutoRenew] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const { user, token } = useSelector((state) => state.auth);
  const currentPlan = user?.activePlanDetails;

  const { data: selectedPlan, isLoading: isPlanLoading } = useDataFetch({
    queryKey: '/plans',
    endPoint: `/plans/${planId}`,
    enabled: !!planId,
  });

  // console.log(planId);
  const {
    data,
    isLoading: cartLoading,
    refetch: cartRefetch,
  } = useDataFetch({
    queryKey: '/cart-items',
    endPoint: '/shop/cart',
    enabled: !planId,
  });

  const cartItems = data?.items || [];

  const handleDeleteFromCart = async (id) => {
    console.log(id);
    try {
      await api.delete(`/shop/cart/items/${id}`);
      cartRefetch();
    } catch (error) {
      console.log(error);
    }
  };

  const handleCheckout = async () => {
    let payload =
      method === 'reward_points'
        ? {
            paymentMethod: method,
            // useRewardPoints: true,
            // paymentDetails: {
            //   cardToken: 'tok_visa',
            // },
            returnUrl: '/payment/success',
            cancelUrl: '/payment/failed',
            // promoCode: 'SUMMER20',
          }
        : method === 'free'
        ? {
            planId: planId,
            autoRenew: false,
          }
        : {
            paymentMethod: method,
            returnUrl: '/payment/success',
            cancelUrl: '/payment/failed',
          };
    try {
      setIsLoading(true);
      let url;
      if (cartItems && cartItems?.length > 0 && !selectedPlan) {
        url = `/shop/cart/checkout`;
      } else {
        payload.autoRenew = autoRenew;
        payload.planId = planId;
        if (currentPlan) {
          url = `/plans/upgrade`;
        } else {
          if (method === 'free') {
            url = `/plans/subscribe/free`;
          } else {
            url = `/plans/subscribe`;
          }
        }
      }

      const response = await api.post(url, payload);
      console.log(response?.data?.access_token);
      if (method === 'free') {
        if (response?.data) {
          try {
            const meResponse = await api.get('/auth/me');
            dispatch(
              loginSuccess({
                user: { ...meResponse?.data },
                token: response?.data?.access_token,
                token_expires: meResponse?.data?.expiresAt,
              })
            );

            router.push('/diary');
          } catch (error) {
            console.log(error);
          } finally {
            setIsLoading(false);
          }
        }
        return;
      }

      // !planId && cartRefetch();
      const fullURL = response?.data?.paymentUrl;

      if (fullURL) {
        const endpoint = fullURL.replace(/^https?:\/\/[^\/]+/, '');
        // console.log(endpoint);
        router.push(endpoint);
      } else {
        if (method !== 'reward_points') {
          alert('Payment URL not found. Please try again.');
        }
      }
    } catch (error) {
      console.log(error);
    } finally {
      setIsLoading(false);
    }
  };

  // const handleFreeCheckout = async () => {
  //   let payload = {
  //     planId: planId,
  //     autoRenew: false,
  //     reason: 'Development testing',
  //   };

  //   try {
  //     setIsLoading(true);
  //     await api.post('/plans/subscribe/free', payload);
  //     setIsLoading(false);
  //   } catch (error) {
  //     console.log(error);
  //   } finally {
  //     setIsLoading(false);
  //   }
  // };

  // const methodOptions = [
  //   { value: '************', label: 'Credit Card' },
  //   { value: '************', label: 'Bank transfer' },
  //   { value: '************', label: 'Virtual account' },
  //   { value: '************', label: 'Points' },
  //   { value: '************', label: 'Mobile phone' },
  //   { value: '************', label: 'Gift certificate' },
  // ];

  const methodOptions = [
    { value: 'kcp_card', label: 'KCP Card' },
    // { value: 'credit_card', label: 'Credit Card' },
    { value: 'kcp_virtual_account', label: 'Virtual account' },
    { value: 'kcp_bank', label: 'Bank transfer' },
    !planId && { value: 'reward_points', label: 'Reward Points' },
    { value: 'kcp_mobile', label: 'KCP Mobile' },
    planId && { value: 'free', label: 'Free' },
  ].filter(Boolean);

  return (
    <div className="py-4 max-w-7xl mx-auto px-4 sm:px-5 xl:px-0">
      <GoBack title={'Order Checkout'} linkClass="my-5 w-full max-w-52" />
      <div className="space-y-8 rounded-lg overflow-hidden z-30">
        <div>
          <div className="pb-4">
            <div className="p-4 pb-3 border-b-2 border-yellow-400 flex items-center justify-between">
              <h3 className="font-bold text-lg md:text-xl text-yellow-500">
                Order
              </h3>
            </div>

            {cartItems && cartItems?.length > 0 && !selectedPlan ? (
              <>
                <div className="overflow-y-auto ">
                  {cartItems.map((item) => (
                    <div key={item.id} className="p-4 border-b flex">
                      <div className="flex-shrink-0 mr-3">
                        <Image
                          src={
                            item?.imageUrl || 'https://via.placeholder.com/50'
                          }
                          alt={item.title}
                          className="h-16 w-16 md:h-20 md:w-20 object-cover rounded"
                          width={100}
                          height={100}
                          onError={(e) => {
                            e.target.onerror = null;
                            e.target.src = 'https://via.placeholder.com/50';
                          }}
                        />
                      </div>
                      <div className="flex-grow">
                        <h4 className="font-medium text-base md:text-lg">
                          {item.title}
                        </h4>
                        <p className="text-xs text-gray-500">
                          {item.categoryName}
                        </p>
                        <div className="flex items-center mt-1 flex-wrap">
                          {item.isOnSale && item.discountPercentage > 0 ? (
                            <>
                              <span className="text-sm font-bold">
                                ${item.price.toFixed(2)}
                              </span>
                              <span className="text-xs text-gray-500 line-through ml-2">
                                ${item.originalPrice.toFixed(2)}
                              </span>
                              {/* <span className="text-xs text-green-600 ml-2">
                                ({item.discountPercentage}% off)
                              </span> */}
                            </>
                          ) : (
                            <span className="text-sm font-bold">
                              ${item.originalPrice.toFixed(2)}
                            </span>
                          )}
                          <span className="text-xs text-gray-500 ml-2">
                            x {item.quantity}
                          </span>
                        </div>
                      </div>
                      <button
                        onClick={() => handleDeleteFromCart(item.id)}
                        className="text-gray-400 hover:text-red-500 transition self-start"
                        aria-label="Remove item"
                      >
                        <Icon
                          icon="mdi:trash-can-outline"
                          className="w-5 h-5"
                        />
                      </button>
                    </div>
                  ))}
                </div>
              </>
            ) : selectedPlan ? (
              <div className="p-4 border-b flex">
                <div className="flex-shrink-0 mr-3">
                  <div className="text-xs bg-gray-100 h-full w-full flex items-center justify-center p-5 rounded-lg shadow">
                    {selectedPlan?.name}
                  </div>
                </div>
                <div className="flex-grow">
                  <h4 className="font-medium text-base md:text-lg">
                    {selectedPlan.name}
                  </h4>
                  <p className="text-xs text-gray-500">
                    {selectedPlan.description}
                  </p>
                  <div className="flex items-center mt-1 flex-wrap">
                    <>
                      <span className="text-sm font-bold">
                        ${selectedPlan?.price}
                      </span>
                    </>
                    <span className="text-xs text-gray-500 ml-2">x 1</span>
                  </div>
                </div>
              </div>
            ) : (
              <div className="p-4 text-center text-gray-500">
                <Icon
                  icon="mdi:cart-remove"
                  className="w-10 h-10 mx-auto text-gray-300 mb-2"
                />
                No items in cart
              </div>
            )}
          </div>

          <div className=" border-b">
            <div className="p-4 pb-3 border-b-2 border-yellow-400 flex items-center justify-between">
              <h3 className="font-bold text-lg md:text-xl text-yellow-500">
                Payment Method
              </h3>
            </div>

            {/* Desktop View - Grid Layout */}
            <div className="hidden md:grid grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 items-center gap-5 p-6">
              {methodOptions?.map((option) => (
                <div key={option.value} className="flex items-center">
                  <input
                    type="radio"
                    id={`method-${option.value}`}
                    value={option.value}
                    checked={method === option.value}
                    onChange={() => setMethod(option.value)}
                    className="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 cursor-pointer"
                  />
                  <label
                    htmlFor={`method-${option.value}`}
                    className="ml-2 text-sm text-gray-700 cursor-pointer"
                  >
                    {option.label}
                  </label>
                </div>
              ))}
            </div>

            {/* Mobile View - Dropdown Selector */}
            <div className="md:hidden p-4">
              <label htmlFor="payment-method" className="sr-only">
                Payment Method
              </label>
              <select
                id="payment-method"
                value={method}
                onChange={(e) => setMethod(e.target.value)}
                className="block w-full rounded-md border-gray-300 py-2 pl-2 border pr-10 text-base focus:border-yellow-500 focus:outline-none focus:ring-yellow-500"
              >
                {methodOptions.map((option) => (
                  <option key={option.value} value={option.value}>
                    {option.label}
                  </option>
                ))}
              </select>
            </div>
          </div>

          {planId && (
            <div className="mt-5 px-5">
              <label
                htmlFor="auto-renew"
                className="block text-sm font-medium text-gray-700"
              >
                Auto-Renew
              </label>
              <div className="hidden md:grid grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 items-center gap-5 p-6">
                <div className="flex items-center">
                  <button
                    type="button"
                    onClick={() => setAutoRenew(!autoRenew)}
                    className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors focus:outline-none focus:ring-2 focus:ring-yellow-500 focus:ring-offset-2 ${
                      autoRenew ? 'bg-yellow-500' : 'bg-gray-200'
                    }`}
                  >
                    <span
                      className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                        autoRenew ? 'translate-x-6' : 'translate-x-1'
                      }`}
                    />
                  </button>
                  <label
                    htmlFor={`method`}
                    className="ml-3 text-sm text-gray-700 cursor-pointer"
                    onClick={() => setAutoRenew(!autoRenew)}
                  >
                    {autoRenew ? 'Yes' : 'No'}
                  </label>
                </div>
              </div>
            </div>
          )}
        </div>
        <div className="p-4 md:p-6 ">
          <div className="flex flex-col sm:flex-row items-center gap-3 sm:gap-4 justify-center max-w-2xl mx-auto">
            <button
              onClick={() => router.back()}
              className="w-full bg-gray-400 hover:bg-gray-500 text-white py-2 px-4 rounded transition flex items-center justify-center"
            >
              Go Back
            </button>

            <button
              onClick={handleCheckout}
              disabled={
                cartItems &&
                !cartItems?.length > 0 &&
                !selectedPlan?.id &&
                !isLoading
              }
              className={`w-full py-2 px-4 rounded transition flex items-center justify-center ${
                cartItems &&
                !cartItems?.length > 0 &&
                !selectedPlan?.id &&
                !isLoading
                  ? 'bg-gray-300 cursor-not-allowed'
                  : 'bg-yellow-500 hover:bg-yellow-600 text-white'
              }`}
            >
              {isLoading
                ? 'Processing...'
                : method === 'free'
                ? `${currentPlan ? 'Upgrade' : 'Subscribe'} to Free`
                : 'Proceed to Checkout'}
              <Icon icon="mdi:arrow-right" className="ml-2" />
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default OrderCheckout;
