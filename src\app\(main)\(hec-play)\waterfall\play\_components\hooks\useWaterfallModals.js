import { useDispatch, useSelector } from 'react-redux';
import {
  setShowTimeExpiredModal,
  setShowAnswerCheckModal,
  closeAllModals,
  selectShowTimeExpiredModal,
  selectShowAnswerCheckModal,
  selectGameCompleted,
  selectIsSubmitting,
  selectSubmissionInitiated,
  selectCurrentQuestionIndex,
  selectQuestions,
} from '@/store/features/waterfallSlice';
import { useWaterfallQuestion } from './useWaterfallQuestion';
import { useWaterfallGame } from './useWaterfallGame';
import { useWaterfallSubmission } from './useWaterfallSubmission';

export const useWaterfallModals = (timerHook = null) => {
  const dispatch = useDispatch();
  const { resetCurrentQuestion } = useWaterfallQuestion();
  const { proceedToNextQuestion } = useWaterfallGame();
  const { submitGame } = useWaterfallSubmission();

  const showTimeExpiredModal = useSelector(selectShowTimeExpiredModal);
  const showAnswerCheckModal = useSelector(selectShowAnswerCheckModal);
  const gameCompleted = useSelector(selectGameCompleted);
  const isSubmitting = useSelector(selectIsSubmitting);
  const submissionInitiated = useSelector(selectSubmissionInitiated);
  const hasChecked = useSelector(state => state.waterfall.hasChecked);
  const blanks = useSelector(state => state.waterfall.blanks);
  const currentQuestionIndex = useSelector(selectCurrentQuestionIndex);
  const questions = useSelector(selectQuestions);

  // Handle timer expiration
  const handleTimeExpired = () => {
    console.log('handleTimeExpired called', {
      gameCompleted,
      isSubmitting,
      submissionInitiated,
      showAnswerCheckModal,
      showTimeExpiredModal
    });

    // Prevent multiple calls
    if (gameCompleted || isSubmitting || submissionInitiated) {
      console.log('Timer expiration blocked due to game state');
      return;
    }

    // If answer check modal is already open, don't show time expired modal
    if (showAnswerCheckModal) {
      console.log('Timer expiration blocked due to answer check modal being open');
      return;
    }

    // If time expired modal is already showing, don't show it again
    if (showTimeExpiredModal) {
      console.log('Timer expiration blocked due to time expired modal already being open');
      return;
    }

    console.log('Timer expired, showing time expired modal');
    dispatch(setShowTimeExpiredModal(true));
  };

  // Unified function to proceed to next question - used by both modals
  const proceedToNextQuestionUnified = () => {
    // Create empty answer data for skipped question if no answer was provided
    if (!hasChecked && blanks.every((blank) => blank === null)) {
      // This will be handled by the submission hook
    }

    // Move to next question or finish game
    const result = proceedToNextQuestion();
    return result;
  };

  // Handle Time Expired Modal actions
  const handlePlayAgain = () => {
    dispatch(closeAllModals());
    resetCurrentQuestion();

    // Restart the timer for the current question
    if (timerHook && timerHook.restartCurrentTimer) {
      timerHook.restartCurrentTimer();
    }
  };

  const handleSkipQuestion = () => {
    dispatch(closeAllModals());

    // Check if this is the last question
    const isLastQuestion = currentQuestionIndex === questions.length - 1;

    if (isLastQuestion) {
      // For the last question, submit the game (even with empty answers)
      submitGame();
    } else {
      // For other questions, proceed to next question
      proceedToNextQuestionUnified();
    }
  };

  // Handle Answer Check Modal actions
  const handleAnswerModalPlayAgain = () => {
    dispatch(closeAllModals());
    resetCurrentQuestion();

    // Restart the timer for the current question
    if (timerHook && timerHook.restartCurrentTimer) {
      timerHook.restartCurrentTimer();
    }
  };

  const handleAnswerModalNextChallenge = () => {
    dispatch(closeAllModals());

    // Check if this is the last question
    const isLastQuestion = currentQuestionIndex === questions.length - 1;

    if (isLastQuestion) {
      // For the last question, submit the game
      submitGame();
    } else {
      // For other questions, proceed to next question
      proceedToNextQuestionUnified();
    }
  };

  // Show answer check modal
  const showAnswerCheckModalAction = () => {
    dispatch(setShowAnswerCheckModal(true));
  };

  return {
    // State
    showTimeExpiredModal,
    showAnswerCheckModal,
    
    // Timer expiration
    handleTimeExpired,
    
    // Time Expired Modal actions
    handlePlayAgain,
    handleSkipQuestion,
    
    // Answer Check Modal actions
    handleAnswerModalPlayAgain,
    handleAnswerModalNextChallenge,
    showAnswerCheckModalAction,
    
    // Utility
    proceedToNextQuestionUnified,
  };
};
