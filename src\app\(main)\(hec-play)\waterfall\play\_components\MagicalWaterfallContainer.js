'use client';
import React from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { DraggableOption } from './WaterfallGame2';

const MagicalWaterfallContainer = ({
  options,
  activeId,
  isPaused = false
}) => {
  return (
    <motion.div
      initial={{ opacity: 0, y: 30 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.7, ease: "easeOut" }}
      className="relative min-h-[270px] sm:min-h-[220px] overflow-hidden md:min-h-[250px] mb-8 rounded-xl z-30"
    >
      {/* Optimized floating sparkles animation - Reduced count and complexity */}
      <div className="absolute inset-0 pointer-events-none">
        {[...Array(4)].map((_, i) => (
          <motion.div
            key={i}
            className="absolute w-1 h-1 bg-gradient-to-r from-yellow-300 to-orange-400 rounded-full opacity-60"
            animate={isPaused ? {} : {
              x: [0, 80, 0],
              y: [0, -40, 0],
              opacity: [0.3, 0.8, 0.3],
              scale: [1, 1.5, 1],
            }}
            transition={isPaused ? {} : {
              duration: 6 + i * 1.5,
              repeat: Infinity,
              delay: i * 1.5,
              ease: "linear"
            }}
            style={{
              left: `${10 + i * 20}%`,
              top: `${20 + (i % 2) * 30}%`,
              willChange: 'transform, opacity',
            }}
          />
        ))}

      </div>
      

      
      {/* Options container with enhanced effects */}
      <div id="options-container" className="w-full h-full relative">
        <AnimatePresence mode="popLayout">
          {options
            ?.filter((option) => !option.used)
            .map((option, filteredIndex) => (
              <DraggableOption
                key={option.id}
                id={option.id}
                option={option.text}
                isActive={activeId === option.id}
                index={filteredIndex}
                totalOptions={options.filter((o) => !o.used).length}
                isPaused={isPaused}
              />
            ))}
        </AnimatePresence>
      </div>
    </motion.div>
  );
};

export default MagicalWaterfallContainer;
