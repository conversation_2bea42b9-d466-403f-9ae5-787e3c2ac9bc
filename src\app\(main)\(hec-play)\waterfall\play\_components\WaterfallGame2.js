'use client';
import React, { useMemo } from 'react';
import { CSS } from '@dnd-kit/utilities';
import { useDraggable, useDroppable } from '@dnd-kit/core';
import { motion } from 'framer-motion';
import { Icon } from '@iconify/react';

// Dynamic color system from block game
const getDynamicWordColors = () => {
  const colors = [
    'linear-gradient(135deg, #ff6b6b, #feca57)',
    'linear-gradient(135deg, #a8e6cf, #3dd5f3)',
    'linear-gradient(135deg, #ff9ff3, #f368e0)',
    'linear-gradient(135deg, #54a0ff, #2e86de)',
    'linear-gradient(135deg, #5f27cd, #341f97)',
    'linear-gradient(135deg, #00d2d3, #01a3a4)',
    'linear-gradient(135deg, #feca57, #ff9ff3)',
    'linear-gradient(135deg, #48dbfb, #0abde3)',
    'linear-gradient(135deg, #1dd1a1, #10ac84)',
    'linear-gradient(135deg, #ff6348, #e17055)',
    'linear-gradient(135deg, #fd79a8, #e84393)',
    'linear-gradient(135deg, #fdcb6e, #e17055)',
  ];
  return colors[Math.floor(Math.random() * colors.length)];
};

// Draggable option component
const DraggableOption = ({ id, option, isActive, index, totalOptions, isPaused = false }) => {
  // Use useMemo to ensure stable positioning that doesn't change on re-renders
  const stablePosition = useMemo(() => {
    const columns = Math.min(Math.max(Math.ceil(totalOptions / 2), 3), 5); // 3-5 columns based on total options
    const col = index % columns;
    const cellWidth = 90 / columns; // Use 90% width with better spacing

    // Create a deterministic "random" offset based on the option text and index
    // This ensures the same option always gets the same offset
    const seed = option.split('').reduce((acc, char) => acc + char.charCodeAt(0), 0) + index;
    const pseudoRandom = (seed * 9301 + 49297) % 233280 / 233280; // Simple PRNG

    // Reduce offset to prevent overlapping and ensure better spacing
    const maxOffset = cellWidth * 0.15; // Smaller offset range
    const xOffset = (pseudoRandom * 2 - 1) * maxOffset;

    // Calculate position with better spacing
    const baseX = 5 + col * cellWidth + cellWidth * 0.5; // 5% margin from left
    const x = Math.max(5, Math.min(95, baseX + xOffset)); // Ensure within bounds

    const rotation = (pseudoRandom * 6 - 3); // Reduced rotation for better readability

    return { x, rotation };
  }, [option, index, totalOptions]);

  // Generate consistent dynamic color for this option
  const dynamicColor = useMemo(() => getDynamicWordColors(), [id]);

  const duration = 8; // 8s per fall for smoother animation
  const delay = index * 0.6 + (stablePosition.x / 100) * 0.3; // Staggered delay based on position and index

  const { attributes, listeners, setNodeRef, transform, isDragging } =
    useDraggable({
      id: id,
      data: {
        option,
        type: 'option', // Specify the type for better collision detection
        index
      },
    });

  const style = transform
    ? {
        transform: CSS.Translate.toString(transform),
        touchAction: 'none',
        zIndex: 1000, // Higher z-index when dragging
        opacity: 0.8, // Slight transparency when dragging
        scale: 1.05, // Slightly larger when dragging
      }
    : {
        touchAction: 'none',
        zIndex: 10,
        opacity: 1,
        scale: 1,
      };

  if (isDragging || isActive) {
    return null;
  }

  // Animate y from -50 to 250 (goes under the box, box is overflow-hidden)
  return (
    <motion.div
      initial={{
        y: -50,
        opacity: 1,
        rotate: stablePosition.rotation * 2,
        scale: 0.8,
      }}
      animate={isPaused ? {} : {
        y: 260, // go further down so it appears to go under the box
        opacity: 1,
        rotate: stablePosition.rotation,
        scale: [0.8, 1, 0.9, 1],
      }}
      transition={isPaused ? {} : {
        type: 'linear',
        duration,
        delay,
        repeat: Infinity,
        repeatType: 'loop',
        repeatDelay: 0,
        ease: 'linear',
        scale: {
          duration: 2,
          repeat: Infinity,
          repeatType: 'reverse',
        }
      }}
      className="absolute"
      style={{
        left: `${stablePosition.x}%`,
        position: 'absolute',
        pointerEvents: 'auto',
        filter: 'drop-shadow(0 2px 8px rgba(0,0,0,0.15))',
      }}
      whileHover={{
        scale: 1.1,
        rotate: stablePosition.rotation + 5,
        transition: { duration: 0.2 }
      }}
    >
      <div
        ref={setNodeRef}
        style={{
          ...style,
          background: dynamicColor,
          color: 'white',
          fontWeight: 'bold',
          textShadow: '0 2px 4px rgba(0,0,0,0.3)',
          border: '2px solid rgba(255,255,255,0.3)',
          boxShadow: '0 4px 15px rgba(0,0,0,0.2)',
        }}
        {...listeners}
        {...attributes}
        className="rounded-md px-2 py-1 cursor-grab hover:shadow-lg transition-shadow text-xs sm:text-sm md:text-base whitespace-nowrap max-w-[120px] sm:max-w-[150px] md:max-w-[200px] overflow-hidden text-ellipsis"
        title={option} // Show full text on hover
      >
        {option}
      </div>
    </motion.div>
  );
};

// Droppable blank component
const DroppableBlank = ({ id, value, index, onReset, correctAnswers }) => {
  const { isOver, setNodeRef, active } = useDroppable({
    id: id,
    data: {
      index,
      accepts: ['option'], // Specify what types of draggables this accepts
    },
  });

  // Determine if the currently dragged option is correct for this blank
  const isCorrectOption = () => {
    if (!active || !correctAnswers) return null;

    // Extract the option index from the active id (format: "option-X")
    const draggedOptionIndex = active.id.startsWith('option-')
      ? parseInt(active.id.split('-')[1])
      : null;

    if (draggedOptionIndex === null) return null;

    // Check if the option being dragged matches the correct answer for this blank
    return correctAnswers[index] === active.data.current?.option;
  };

  // Enhanced magical styling system
  const getMagicalStyles = () => {
    const validationResult = isCorrectOption();

    if (!isOver) {
      return value
        ? {
            // Filled state - magical success styling
            borderClass: 'border-3 border-solid border-blue-500',
            bgClass: 'bg-gradient-to-br from-blue-100 via-indigo-100 to-purple-100',
            shadowClass: 'shadow-xl shadow-blue-300/60',
            textClass: 'text-blue-900 font-bold',
            ringClass: 'ring-2 ring-blue-300/50',
            scaleClass: 'scale-105'
          }
        : {
            // Empty state - magical invitation styling
            borderClass: 'border-3 border-dashed border-purple-400',
            bgClass: 'bg-gradient-to-br from-purple-50 via-pink-50 to-indigo-50',
            shadowClass: 'shadow-lg shadow-purple-300/40',
            textClass: 'text-purple-700 font-semibold',
            ringClass: 'ring-1 ring-purple-200/50',
            scaleClass: 'scale-100'
          };
    }

    // Hover states with enhanced feedback
    if (validationResult === null) {
      return {
        // Unknown correctness - magical mystery styling
        borderClass: 'border-3 border-solid border-yellow-500',
        bgClass: 'bg-gradient-to-br from-yellow-100 via-orange-100 to-amber-100',
        shadowClass: 'shadow-2xl shadow-yellow-400/70',
        textClass: 'text-yellow-900 font-bold',
        ringClass: 'ring-3 ring-yellow-300/60',
        scaleClass: 'scale-105'
      };
    }

    return validationResult
      ? {
          // Correct answer - magical success styling
          borderClass: 'border-3 border-solid border-emerald-500',
          bgClass: 'bg-gradient-to-br from-emerald-100 via-green-100 to-teal-100',
          shadowClass: 'shadow-2xl shadow-emerald-400/70',
          textClass: 'text-emerald-900 font-bold',
          ringClass: 'ring-3 ring-emerald-300/60',
          scaleClass: 'scale-105'
        }
      : {
          // Incorrect answer - magical warning styling
          borderClass: 'border-3 border-solid border-red-500',
          bgClass: 'bg-gradient-to-br from-red-100 via-pink-100 to-rose-100',
          shadowClass: 'shadow-2xl shadow-red-400/70',
          textClass: 'text-red-900 font-bold',
          ringClass: 'ring-3 ring-red-300/60',
          scaleClass: 'scale-105'
        };
  };

  const magicalStyles = getMagicalStyles();

  return (
    <motion.div
      className="inline-flex items-center relative"
      animate={{
        scale: magicalStyles.scaleClass === 'scale-105' ? 1.1 : magicalStyles.scaleClass === 'scale-105' ? 1.05 : 1,
      }}
      transition={{ duration: 0.2, ease: "easeOut" }}
    >
      {/* Enhanced magical aura effects */}
      {isOver && (
        <motion.div
          className="absolute inset-0 pointer-events-none -z-10"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
        >
          {/* Optimized sparkles around drop zone - Reduced count */}
          {[...Array(2)].map((_, i) => (
            <motion.div
              key={i}
              className="absolute w-1 h-1 bg-yellow-400 rounded-full"
              animate={{
                opacity: [0.3, 0.8, 0.3],
                scale: [1, 1.3, 1],
              }}
              transition={{
                duration: 2 + i * 0.5,
                repeat: Infinity,
                delay: i * 0.5,
                ease: "linear"
              }}
              style={{
                left: `${25 + i * 25}%`,
                top: `${25 + i * 25}%`,
                willChange: 'transform, opacity',
              }}
            />
          ))}

          {/* Pulsing magical aura */}
          <motion.div
            className="absolute inset-0 rounded-xl border-2 border-yellow-300/50 pointer-events-none"
            animate={{
              borderColor: [
                "rgba(251, 191, 36, 0.3)",
                "rgba(251, 191, 36, 0.7)",
                "rgba(251, 191, 36, 0.3)"
              ],
              boxShadow: [
                "0 0 10px rgba(251, 191, 36, 0.2)",
                "0 0 25px rgba(251, 191, 36, 0.5)",
                "0 0 10px rgba(251, 191, 36, 0.2)"
              ]
            }}
            transition={{
              duration: 1.5,
              repeat: Infinity,
              ease: "easeInOut"
            }}
          />
        </motion.div>
      )}

      <motion.div
        ref={setNodeRef}
        whileHover={{ scale: 1.02 }}
        className={`
          inline-flex items-center border justify-center min-w-10 sm:min-w-16 h-6 sm:h-8 mx-1 sm:mx-2
          ${magicalStyles.borderClass} ${magicalStyles.bgClass} ${magicalStyles.shadowClass}
          ${magicalStyles.ringClass} rounded-xl px-1 sm:px-2 transition-all duration-200
          text-sm sm:text-base md:text-lg font-medium relative overflow-hidden
          cursor-pointer
        `}
        title='✨ Magical Drop Zone ✨'
        style={{
          backdropFilter: 'blur(8px)',
          // minHeight: '40px', // Ensure minimum hit area
          // minWidth: '60px',
        }}
      >
        {/* Optimized background shimmer - Reduced frequency */}
        <motion.div
          className="absolute inset-0 bg-gradient-to-r from-transparent via-white/15 to-transparent"
          animate={{ x: [-100, 200] }}
          transition={{ duration: 5, repeat: Infinity, repeatDelay: 4 }}
          style={{ willChange: 'transform' }}
        />

        <span className={`truncate max-w-[100px] sm:max-w-[140px] relative z-10 ${magicalStyles.textClass}`} title={value || '✨ Drop Magic Word ✨'}>
          {value ? (
            <motion.span
              initial={{ scale: 0.8, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              className="inline-flex items-center gap-1"
            >
              {value}
            </motion.span>
          ) : (
            <motion.span
              animate={{
                opacity: [0.7, 1, 0.7],
              }}
              transition={{
                duration: 3,
                repeat: Infinity,
                ease: "linear"
              }}
              className="text-sm flex items-center gap-1"
            >
              <span>🎯</span>
              <span className="hidden sm:inline">Drop</span>
            </motion.span>
          )}
        </span>
      </motion.div>

      {/* Enhanced magical reset button */}
      {value && onReset && (
        <motion.button
          onClick={() => onReset(index)}
          whileHover={{
            scale: 1.2,
            rotate: 90,
            boxShadow: "0 0 15px rgba(239, 68, 68, 0.5)"
          }}
          whileTap={{ scale: 0.9 }}
          className="absolute -right-2 sm:-right-3 -top-2 sm:-top-3 bg-gradient-to-br from-red-400 to-pink-500 rounded-full w-6 h-6 sm:w-7 sm:h-7 flex items-center justify-center text-white hover:from-red-500 hover:to-pink-600 shadow-lg border-2 border-white z-20"
          title="🗑️ Remove Magic Word"
        >
          <motion.div
            animate={{ rotate: [0, 360] }}
            transition={{ duration: 4, repeat: Infinity, ease: "linear" }}
            style={{ willChange: 'transform' }}
          >
            <Icon icon="mdi:magic-staff" width={14} className="sm:w-4" />
          </motion.div>
        </motion.button>
      )}
    </motion.div>
  );
};

// Enhanced drag overlay component
const DragOverlayContent = ({ option }) => {
  // Generate dynamic color for drag overlay
  const dynamicColor = useMemo(() => getDynamicWordColors(), [option]);

  return (
    <motion.div
      initial={{ scale: 1.1, rotate: 0 }}
      animate={{ scale: 1.15, rotate: 3 }}
      className="rounded-lg px-4 py-2 shadow-2xl text-sm relative pointer-events-none"
      style={{
        background: dynamicColor,
        color: 'white',
        fontWeight: 'bold',
        textShadow: '0 2px 4px rgba(0,0,0,0.4)',
        border: '3px solid rgba(255,255,255,0.4)',
        zIndex: 10000,
        filter: 'drop-shadow(0 6px 12px rgba(0,0,0,0.3))',
        backdropFilter: 'blur(2px)',
      }}
    >
      <motion.span
        animate={{
          textShadow: [
            '0 2px 4px rgba(0,0,0,0.4)',
            '0 2px 6px rgba(255,255,255,0.2)',
            '0 2px 4px rgba(0,0,0,0.4)'
          ]
        }}
        transition={{ duration: 2, repeat: Infinity, ease: "linear" }}
        style={{ willChange: 'filter' }}
      >
        {option?.text || option}
      </motion.span>
    </motion.div>
  );
};

export {
  DraggableOption,
  DroppableBlank,
  DragOverlayContent,
};
