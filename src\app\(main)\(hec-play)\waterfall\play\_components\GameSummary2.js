'use client';
import React, { useState } from 'react';
import { Icon } from '@iconify/react';
import { motion } from 'framer-motion';
import Link from 'next/link';
import Image from 'next/image';
import { generateSummaryFromServerData } from './utils/answerUtils';

const GameSummary2 = ({ resultData, questionsData, onRestart, onExit }) => {
  // State for managing view answer functionality
  const [viewResult, setViewResult] = useState(null);

  // Extract data from API response
  const {
    set_title,
    total_correct_answers,
    total_questions,
    score,
    answers,
    submitted_at,
  } = resultData;

  questionsData?.forEach((q, i) => {
    const matchingAnswer = answers?.find((a) => a.question_id === q.id);
  });

  return (
    <div className="relative min-h-screen overflow-hidden">
      {/* Magical Sky Background */}

      <div className="relative z-10 min-h-[calc(100vh-200px)] max-w-6xl mb-8 mx-auto">
        {/* Magical Header with Cat and Score */}
        <motion.div
          initial={{ opacity: 0, y: -30, scale: 0.9 }}
          animate={{ opacity: 1, y: 0, scale: 1 }}
          transition={{ duration: 0.8, ease: 'easeOut' }}
          className="bg-gradient-to-br from-purple-50 via-pink-50 to-indigo-50 border-3 border-purple-300 rounded-2xl max-sm:shadow shadow-xl p-5 px-3 sm:p-8 mb-8 relative overflow-hidden"
          style={{
            backdropFilter: 'blur(10px)',
            // boxShadow: '0 20px 40px rgba(147, 51, 234, 0.2), 0 0 30px rgba(59, 130, 246, 0.1)'
          }}
        >
          {/* Magical shimmer effect */}
          <motion.div
            className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent"
            animate={{ x: [-100, 300] }}
            transition={{ duration: 3, repeat: Infinity, repeatDelay: 2 }}
          />

          <div className="flex flex-col lg:flex-row items-center justify-between relative z-10">
            {/* Magical Cat Section */}
            <div className="flex flex-col lg:flex-row items-center mb-6 lg:mb-0">
              <motion.div
                className="w-32 h-32 bg-gradient-to-br from-yellow-200 via-orange-100 to-pink-100 rounded-3xl flex items-center justify-center mr-0 lg:mr-8 mb-4 lg:mb-0 shadow-xl border-3 border-yellow-300"
                animate={{
                  boxShadow: [
                    '0 0 20px rgba(251, 191, 36, 0.3)',
                    '0 0 40px rgba(251, 191, 36, 0.6)',
                    '0 0 20px rgba(251, 191, 36, 0.3)',
                  ],
                }}
                transition={{ duration: 2, repeat: Infinity }}
              >
                <Image
                  src="/assets/images/all-img/catImg.png"
                  alt="Magical Cat"
                  width={100}
                  height={100}
                  className="object-contain"
                />
              </motion.div>
              <div className="text-center lg:text-left">
                <motion.h1
                  className="sm:text-xl lg:text-3xl font-bold bg-gradient-to-r from-yellow-600 via-yellow-400 to-yellow-500 bg-clip-text text-transparent mb-3"
                  animate={{
                    backgroundPosition: ['0%', '100%', '0%'],
                  }}
                  transition={{ duration: 3, repeat: Infinity }}
                >
                  Magical Quest Complete!
                </motion.h1>
                {set_title && (
                  <motion.p
                    className="text-sm sm:text-lg text-yellow-600 font-semibold"
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    transition={{ delay: 0.5 }}
                  >
                    {set_title}
                  </motion.p>
                )}
              </div>
            </div>

            {/* Magical Score Badge */}
            <div
              className="relative"
              initial={{ opacity: 0, scale: 0 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{
                duration: 1,
                delay: 0.3,
                type: 'spring',
                bounce: 0.5,
              }}
            >
              <div className="relative">
                <Image
                  src={'/assets/images/all-img/summaryScore.png'}
                  alt="Magical Medal"
                  width={300}
                  height={150}
                />
                <motion.div
                  className="text-center absolute top-1/3 left-1/4"
                  animate={{
                    scale: [1, 1.05, 1],
                    textShadow: [
                      '0 2px 4px rgba(0,0,0,0.3)',
                      '0 4px 8px rgba(251, 191, 36, 0.5)',
                      '0 2px 4px rgba(0,0,0,0.3)',
                    ],
                  }}
                  transition={{ duration: 2, repeat: Infinity }}
                >
                  <h2 className=" sm:text-xl lg:text-3xl font-bold text-yellow-700">
                    🏆 Score: {score}
                  </h2>
                </motion.div>
              </div>
            </div>
          </div>
        </motion.div>

        {/* Magical Answer Sheet */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.4 }}
          className="max-w-5xl mx-auto mb-8 bg-white rounded-2xl  shadow xl:shadow-xl border-2 border-purple-300"
        >
          <motion.div
            className="bg-gradient-to-r from-indigo-100 via-purple-50 to-pink-100 border-3 border-purple-400 rounded-t-2xl p-3 sm:p-6 sm:py-3 relative overflow-hidden"
            style={{
              backdropFilter: 'blur(8px)',
              // boxShadow: '0 15px 30px rgba(147, 51, 234, 0.15)'
            }}
            animate={{
              borderColor: [
                'rgba(147, 51, 234, 0.4)',
                'rgba(59, 130, 246, 0.4)',
                'rgba(147, 51, 234, 0.4)',
              ],
            }}
            transition={{ duration: 3, repeat: Infinity }}
          >
            <motion.h2
              className="text-xl sm:text-2xl font-bold text-center bg-gradient-to-r from-purple-600 to-blue-600  bg-clip-text text-transparent"
              animate={{
                scale: [1, 1.02, 1],
              }}
              transition={{ duration: 2, repeat: Infinity }}
            >
              Answer Sheet
            </motion.h2>
          </motion.div>

          {questionsData && questionsData.length > 0 && (
            <div className="">
              <div className="space-y-2 max-h-96 overflow-y-auto xl:p-6 custom-scrollbar">
                {questionsData.map((questionData, index) => {
                  // Find the corresponding answer data (if exists)
                  const answer = answers?.find(
                    (a) => a.question_id === questionData.id
                  );

                  // Check if this question was answered correctly
                  const isCorrect = answer?.is_correct || false;
                  const hasUserAnswer =
                    answer &&
                    answer.submitted_answers &&
                    answer.correct_answers;

                  let summaryHTML;

                  // Determine what to show based on viewResult state
                  if (hasUserAnswer) {
                    if (viewResult?.idx === index) {
                      // Show correct answer when "View Answer" is clicked
                      let gapIndex = 0;
                      summaryHTML = questionData.question_text_plain.replace(
                        /\[\[gap\]\]/g,
                        () => {
                          const correctAnswer = `<span class="px-2 rounded-lg bg-gradient-to-r from-green-100 to-emerald-100 text-green-800 font-semibold border border-green-300 shadow-sm">${questionData.correct_answers[gapIndex]}</span>` + ' ';
                          gapIndex++;
                          return correctAnswer;
                        }
                      );
                    } else {
                      // Default: Show user's answer with color coding (right/wrong indicators)
                      summaryHTML = generateSummaryFromServerData(
                        answer,
                        questionData
                      );
                    }
                  } else {
                    // User didn't answer this question - show correct answer in red
                    let gapIndex = 0;
                    summaryHTML = questionData.question_text_plain.replace(
                      /\[\[gap\]\]/g,
                      () => {
                        const correctAnswer = `<span class="px-2 rounded-lg bg-gradient-to-r from-red-100 to-pink-100 text-red-800 font-semibold border border-red-300 shadow-sm">${questionData.correct_answers[gapIndex]}</span>` + ' ';
                        gapIndex++;
                        return correctAnswer;
                      }
                    );
                  }

                  return (
                    <motion.div
                      key={questionData.id}
                      initial={{ opacity: 0, x: -30, scale: 0.95 }}
                      animate={{ opacity: 1, x: 0, scale: 1 }}
                      transition={{
                        duration: 0.2,
                        delay: index * 0.1,
                        type: 'spring',
                        // bounce: 0.3,
                      }}
                      className="bg-gradient-to-r from-white/90 to-blue-50/90 rounded-xl p-3 sm:p-5 sm:py-3 border-2 border-blue-200 shadow-md hover:shadow-lg transition-all duration-300 relative overflow-hidden"
                      whileHover={{
                        // scale: 1.02,
                        boxShadow: '0 10px 25px rgba(59, 130, 246, 0.15)',
                      }}
                    >
                      <div className="flex items-center gap-2">
                        {/* Question number badge */}
                        <h3
                          className="font-bold text-yellow-600"
                        >
                          {index + 1}. 
                        </h3>
                        <div className="flex-1 flex items-center justify-between gap-2">
                          {/* Display the complete sentence with magical color-coded answers */}
                          <motion.div
                            className="text-lg leading-relaxed font-medium text-gray-800"
                            dangerouslySetInnerHTML={{
                              __html: summaryHTML,
                            }}
                            initial={{ opacity: 0 }}
                            animate={{ opacity: 1 }}
                            transition={{ delay: index * 0.1 + 0.3 }}
                          />

                          {/* View Answer Button - only show if user answered incorrectly */}
                          {hasUserAnswer && !isCorrect && (
                            <motion.div
                              className=" flex justify-end"
                              initial={{ opacity: 0, scale: 0.9 }}
                              animate={{ opacity: 1, scale: 1 }}
                              transition={{ delay: index * 0.1 + 0.5 }}
                            >
                              <button
                                onClick={() =>
                                  setViewResult((prev) =>
                                    prev?.idx === index
                                      ? null // Toggle back to user answer
                                      : {
                                          idx: index,
                                        }
                                  )
                                }
                                className="px-4 py-2 min-w-[120px] rounded-lg bg-gradient-to-r from-yellow-200 to-yellow-300 hover:from-yellow-300 hover:to-yellow-400 text-yellow-800 text-[10px] font-semibold border border-yellow-400 shadow-md hover:shadow-lg transition-all duration-200 flex items-center gap-2"
                              >
                                <Icon
                                  icon={
                                    viewResult?.idx === index
                                      ? 'mdi:eye-off'
                                      : 'mdi:eye'
                                  }
                                  className="text-sm"
                                />
                                {viewResult?.idx === index
                                  ? 'Hide Answer'
                                  : 'View Answer'}
                              </button>
                            </motion.div>
                          )}
                        </div>
                      </div>
                    </motion.div>
                  );
                })}
              </div>
            </div>
          )}
        </motion.div>

        {/* Magical Action Buttons */}
        <motion.div
          initial={{ opacity: 0, y: 30, scale: 0.9 }}
          animate={{ opacity: 1, y: 0, scale: 1 }}
          transition={{
            duration: 0.8,
            delay: 0.6,
            type: 'spring',
            bounce: 0.4,
          }}
          className="flex flex-col sm:flex-row justify-center gap-3 sm:gap-6 mt-8"
        >
          <motion.button
            onClick={onRestart}
            className="bg-gradient-to-r from-yellow-500 to-yellow-500 hover:from-yellow-500 hover:to-yellow-500 text-white px-10 py-4 rounded-2xl font-bold text-lg shadow lg:shadow-xl transition-all duration-300 flex items-center justify-center gap-3 relative overflow-hidden group"
            whileTap={{ scale: 0.95 }}
            // style={{
            //   background: 'linear-gradient(135deg, #8b5cf6, #ec4899, #8b5cf6)',
            //   backgroundSize: '200% 200%',
            // }}
          >
            {/* Magical shimmer effect */}
            <motion.div
              className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent"
              animate={{ x: [-100, 200] }}
              transition={{ duration: 2, repeat: Infinity, repeatDelay: 1 }}
            />
            <span className="relative z-10">Play Again </span>
            <motion.div
              animate={{ rotate: [0, -360] }}
              transition={{ duration: 2, repeat: Infinity }}
            >
              <Icon icon="mdi:refresh" className="text-2xl" />
            </motion.div>
          </motion.button>

          <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
            <button
              onClick={onExit}
              className="w-full bg-gradient-to-r from-blue-100 via-indigo-100 to-purple-100 hover:from-blue-200 hover:via-indigo-200 hover:to-purple-200 text-purple-700 px-10 py-4 rounded-2xl font-bold text-lg transition-all duration-300 flex items-center justify-center gap-2 border-2 border-purple-300 shadow-lg relative overflow-hidden group"
            >
              {/* Magical border glow */}
              <motion.div
                className="absolute inset-0 rounded-2xl border-2 border-purple-400 opacity-0 group-hover:opacity-100"
                animate={{
                  boxShadow: [
                    '0 0 10px rgba(147, 51, 234, 0.3)',
                    '0 0 20px rgba(147, 51, 234, 0.6)',
                    '0 0 10px rgba(147, 51, 234, 0.3)',
                  ],
                }}
                transition={{ duration: 2, repeat: Infinity }}
              />

              <Icon icon="mdi:arrow-left" className="text-xl" />
              <span>Exit Game</span>
            </button>
          </motion.div>
        </motion.div>
      </div>
    </div>
  );
};

export default GameSummary2;
