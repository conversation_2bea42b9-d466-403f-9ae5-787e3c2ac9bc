import React, { useState } from 'react';
import DiaryPage from './DiaryPage';
import DiaryIconsSidebar from './DiaryIconsSidebar';
import Button from '@/components/Button';
import { useRouter } from 'next/navigation';

const DiaryContent = ({ entries, currentIndex, isMobile = false, isLoading = false, isInitialLoad = false }) => {
  const router = useRouter();

  // For mobile: show only one entry, for desktop: show two entries (spread)
  const hasNextEntry = !isMobile && !isLoading && currentIndex + 1 < entries.length;

  // Centralized drawer state management
  const [openDrawerId, setOpenDrawerId] = useState(null);

  const handleDrawerToggle = (entryId) => {
    // If the same drawer is clicked, close it; otherwise, open the new one
    setOpenDrawerId(openDrawerId === entryId ? null : entryId);
  };

  // Loading skeleton component for diary pages (for page transitions)
  const LoadingPage = ({ className }) => (
    <div className={`bg-[#FDE7E9] ${className}`}>
      <div className="p-6 h-full flex flex-col">
        <div className="animate-pulse">
          {/* Date skeleton */}
          <div className="h-4 bg-gray-300 rounded w-24 mb-4"></div>

          {/* Title skeleton */}
          <div className="h-6 bg-gray-300 rounded w-3/4 mb-6"></div>

          {/* Content skeleton */}
          <div className="space-y-3 flex-1">
            <div className="h-4 bg-gray-300 rounded w-full"></div>
            <div className="h-4 bg-gray-300 rounded w-5/6"></div>
            <div className="h-4 bg-gray-300 rounded w-4/5"></div>
            <div className="h-4 bg-gray-300 rounded w-full"></div>
            <div className="h-4 bg-gray-300 rounded w-3/4"></div>
            <div className="h-4 bg-gray-300 rounded w-5/6"></div>
            <div className="h-4 bg-gray-300 rounded w-2/3"></div>
          </div>

          {/* Bottom elements skeleton */}
          <div className="mt-6 flex justify-between items-center">
            <div className="h-8 bg-gray-300 rounded w-16"></div>
            <div className="h-8 bg-gray-300 rounded w-20"></div>
          </div>
        </div>
      </div>
    </div>
  );

  // Book cover loading component (for initial load)
  const BookCoverLoading = () => (
    <div className="absolute inset-0 flex items-center justify-center">
      <div className={`${isMobile ? 'w-full max-w-sm' : 'w-full max-w-md'} bg-gradient-to-br from-amber-100 to-amber-200 rounded-lg shadow-2xl border-2 border-amber-300`}>
        <div className="p-8 h-full flex flex-col items-center justify-center text-center space-y-6">
          {/* Book icon or illustration */}
          <div className="w-20 h-20 bg-amber-300 rounded-full flex items-center justify-center animate-pulse">
            <svg className="w-10 h-10 text-amber-600" fill="currentColor" viewBox="0 0 20 20">
              <path d="M9 4.804A7.968 7.968 0 005.5 4c-1.255 0-2.443.29-3.5.804v10A7.969 7.969 0 015.5 14c1.669 0 3.218.51 4.5 1.385A7.962 7.962 0 0114.5 14c1.255 0 2.443.29 3.5.804v-10A7.968 7.968 0 0014.5 4c-1.255 0-2.443.29-3.5.804V12a1 1 0 11-2 0V4.804z"/>
            </svg>
          </div>

          {/* Title */}
          <div className="space-y-2">
            <h3 className="text-xl font-semibold text-amber-800 animate-pulse">My Diary</h3>
            <p className="text-amber-600 text-sm animate-pulse">Loading your memories...</p>
          </div>

          {/* Loading animation */}
          <div className="flex space-x-1">
            <div className="w-2 h-2 bg-amber-500 rounded-full animate-bounce"></div>
            <div className="w-2 h-2 bg-amber-500 rounded-full animate-bounce" style={{animationDelay: '0.1s'}}></div>
            <div className="w-2 h-2 bg-amber-500 rounded-full animate-bounce" style={{animationDelay: '0.2s'}}></div>
          </div>

          {/* Decorative elements */}
          <div className="absolute top-4 right-4 w-8 h-8 border-2 border-amber-400 rounded-full opacity-30 animate-ping"></div>
          <div className="absolute bottom-4 left-4 w-6 h-6 border-2 border-amber-400 rounded-full opacity-20 animate-ping" style={{animationDelay: '0.5s'}}></div>
        </div>
      </div>
    </div>
  );

  if (isLoading) {
    // Show book cover for initial load, skeleton for page transitions
    if (isInitialLoad) {
      return <BookCoverLoading />;
    }

    // Show skeleton pages for page transitions
    return (
      <div className="absolute inset-0 flex max-sm:flex-col sm:overflow-hidden flipbook-container">
        {/* Diary spine effect for desktop */}
        {!isMobile && <div className="diary-spine bg-white" />}

        {/* Left page loading */}
        <LoadingPage
          className={`${isMobile ? 'w-full rounded-md min-h-[600px] h-full bg-white' : 'sm:w-1/2 rounded-tl-md rounded-bl-md border-r border-gray-300 bg-white'}`}
        />

        {/* Right page loading - only shown on desktop */}
        {!isMobile && (
          <LoadingPage
            className="sm:w-1/2 rounded-tr-md rounded-br-md bg-white"
          />
        )}
      </div>
    );
  }

  return (
    <div className="absolute inset-0 flex max-sm:flex-col sm:overflow-hidden flipbook-container">
      {/* Diary spine effect for desktop */}
      {!isMobile && <div className="diary-spine" />}

      {/* Left page - always shown */}
      <div
        className={`${isMobile ? 'w-full' : 'sm:w-1/2'} bg-[#FDE7E9] ${
          isMobile ? 'rounded-md min-h-[600px] h-full' : 'rounded-tl-md rounded-bl-md'
        } ${
          hasNextEntry ? 'border-r border-gray-300' : ''
        } flipbook-page page-curl`}
      >
        <DiaryPage
          entry={entries[currentIndex]}
          isDrawerOpen={openDrawerId === entries[currentIndex]?.id}
          onDrawerToggle={() => handleDrawerToggle(entries[currentIndex]?.id)}
        />
      </div>

      {/* Right page - only shown on desktop if there's a next entry */}
      {hasNextEntry ? (
        <div className="sm:w-1/2 bg-[#FDE7E9] rounded-tr-md rounded-br-md flipbook-page page-curl">
          <DiaryPage
            entry={entries[currentIndex + 1]}
            isDrawerOpen={openDrawerId === entries[currentIndex + 1]?.id}
            onDrawerToggle={() =>
              handleDrawerToggle(entries[currentIndex + 1]?.id)
            }
          />
        </div>
      ) : !isMobile ? (
        <div className="h-[98%] my-auto w-full bg-white p-2 rounded-lg flex items-center justify-center flipbook-page">
          <div className="text-center space-y-3">
            <span>End of Diary</span>
            <Button
              buttonText={'Write Diary'}
              onClick={() => router.push('/diary')}
            ></Button>
          </div>
        </div>
      ) : null}
    </div>
  );
};

export default DiaryContent;
