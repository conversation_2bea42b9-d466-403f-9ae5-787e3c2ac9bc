'use client';
import React from "react";
import { useDispatch, useSelector } from "react-redux";
import { 
  selectDecorationItems,
  selectSelectedDecorationId,
  setSelectedDecorationId
} from "@/store/features/diarySlice";
import DecorationWrapper from "./DecorationWrapper";

const DecorationLayer = ({ containerWidth, containerHeight, isDecorating, decorationItems: propDecorationItems, scale = 1, isMobile = false, isTouch = false }) => {
  const dispatch = useDispatch();
  const reduxDecorationItems = useSelector(selectDecorationItems);
  const selectedId = useSelector(selectSelectedDecorationId);

  // Use prop decorations if provided, otherwise use Redux state
  // This ensures we don't fall back to Redux state when props are explicitly passed (even if empty)
  const decorationItems = propDecorationItems !== undefined ? propDecorationItems : reduxDecorationItems;
  
  // Get activeTool safely from Redux state
  const activeTool = useSelector((state) => state.diary?.activeTool) || 'decoration';

  const handleSelect = (id) => {
    // Only allow selection when decorating
    if (isDecorating) {
      dispatch(setSelectedDecorationId(id === selectedId ? null : id));
    }
  };

  const handleLayerClick = (e) => {
    // Only handle clicks when decorating
    if (isDecorating && e.target === e.currentTarget) {
      dispatch(setSelectedDecorationId(null));
    }
  };

  // Show decorations if they exist, regardless of decorating mode
  if (!decorationItems || decorationItems.length === 0) return null;

  return (
    <div
      className={`absolute inset-0 decoration-layer ${
        isDecorating ? 'pointer-events-auto' : 'pointer-events-none'
      } ${isMobile ? 'mobile-optimized' : ''}`}
      style={{
        width: '100%',
        height: '100%',
        zIndex: 5,
        touchAction: isTouch ? 'manipulation' : 'auto'
      }}
      onClick={handleLayerClick}
    >
      {decorationItems.map((item) => (
        <DecorationWrapper
          key={item.id}
          id={item.id}
          item={item}
          selectedId={isDecorating ? selectedId : null} // Only show selection when decorating
          onSelect={handleSelect}
          activeTool={activeTool}
          isDecorating={isDecorating}
          scale={scale}
          isMobile={isMobile}
          isTouch={isTouch}
        />
      ))}
    </div>
  );
};

export default DecorationLayer;