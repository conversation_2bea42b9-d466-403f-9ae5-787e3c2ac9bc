import { createSlice } from '@reduxjs/toolkit';

const initialState = {
  friendActiveTab: 'search',
  openCreateSkinModal: false,
  editSkinModalId: null,
  deleteSkinModalData: null,
  selectedPlan: null,
  // My Diary pagination and open states (non-persistent)
  myDiary: {
    isOpen: false,
    currentIndex: 0,
    currentPage: 1,
    coverPhotoUrl: null,
  },
};

const commonSlice = createSlice({
  name: 'common',
  initialState,
  reducers: {
    setFriendActiveTab: (state, action) => {
      state.friendActiveTab = action.payload;
    },
    setOpenCreateSkinModal: (state, action) => {
      state.openCreateSkinModal = action.payload;
    },
    setEditSkinModalId: (state, action) => {
      state.editSkinModalId = action.payload;
    },
    setDeleteSkinModalData: (state, action) => {
      state.deleteSkinModalData = action.payload;
    },
    setSelectedPlan: (state, action) => {
      state.selectedPlan = action.payload;
    },
    // My Diary state management actions
    setMyDiaryIsOpen: (state, action) => {
      state.myDiary.isOpen = action.payload;
    },
    setMyDiaryCurrentIndex: (state, action) => {
      state.myDiary.currentIndex = action.payload;
    },
    setMyDiaryCurrentPage: (state, action) => {
      state.myDiary.currentPage = action.payload;
    },
    setMyDiaryCoverPhotoUrl: (state, action) => {
      state.myDiary.coverPhotoUrl = action.payload;
    },
    resetMyDiaryState: (state) => {
      state.myDiary = {
        isOpen: false,
        currentIndex: 0,
        currentPage: 1,
        coverPhotoUrl: null,
      };
    },
  },
});

export const {
  setFriendActiveTab,
  setOpenCreateSkinModal,
  setEditSkinModalId,
  setDeleteSkinModalData,
  setSelectedPlan,
  // My Diary actions
  setMyDiaryIsOpen,
  setMyDiaryCurrentIndex,
  setMyDiaryCurrentPage,
  setMyDiaryCoverPhotoUrl,
  resetMyDiaryState,
} = commonSlice.actions;

// Selectors
export const selectFriendActiveTab = (state) => state.common.friendActiveTab;
export const selectOpenCreateSkinModal = (state) => state.common.openCreateSkinModal;
export const selectEditSkinModalId = (state) => state.common.editSkinModalId;
export const selectDeleteSkinModalData = (state) => state.common.deleteSkinModalData;
export const selectSelectedPlan = (state) => state.common.selectedPlan;

// My Diary selectors
export const selectMyDiaryIsOpen = (state) => state.common.myDiary.isOpen;
export const selectMyDiaryCurrentIndex = (state) => state.common.myDiary.currentIndex;
export const selectMyDiaryCurrentPage = (state) => state.common.myDiary.currentPage;
export const selectMyDiaryCoverPhotoUrl = (state) => state.common.myDiary.coverPhotoUrl;
export const selectMyDiaryState = (state) => state.common.myDiary;

export default commonSlice.reducer;
