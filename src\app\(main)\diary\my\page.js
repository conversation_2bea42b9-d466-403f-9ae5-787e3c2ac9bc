'use client';
import { useState, useMemo, useCallback, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { ButtonIcon } from '@/components/Button';
import useDataFetch from '@/hooks/useDataFetch';
import DiaryCover from '../_component/DiaryCover';
import DiaryContent from '../_component/DiaryContent';
import DiaryPagination from '../_component/DiaryPagination';
import DiaryIconsSidebar from '../_component/DiaryIconsSidebar';
import CalendarFilter from '../_component/modalContents/share/CalendarFilter';
import {
  setMyDiaryIsOpen,
  setMyDiaryCurrentIndex,
  setMyDiaryCurrentPage,
  setMyDiaryCoverPhotoUrl,
  resetMyDiaryState,
  selectMyDiaryIsOpen,
  selectMyDiaryCurrentIndex,
  selectMyDiaryCurrentPage,
  selectMyDiaryCoverPhotoUrl,
} from '@/store/features/commonSlice';

export default function MyDiary() {
  const dispatch = useDispatch();

  // Get state from Redux instead of localStorage
  const isOpen = useSelector(selectMyDiaryIsOpen);
  const currentIndex = useSelector(selectMyDiaryCurrentIndex);
  const currentPage = useSelector(selectMyDiaryCurrentPage);
  const coverPhotoUrl = useSelector(selectMyDiaryCoverPhotoUrl);

  const [showCalendar, setShowCalendar] = useState(false);
  const [isMobile, setIsMobile] = useState(false);
  const [hasInitiallyLoaded, setHasInitiallyLoaded] = useState(false);

  // Detect mobile screen size
  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 640); // sm breakpoint
    };

    checkMobile();
    window.addEventListener('resize', checkMobile);

    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  // Modified API call with pagination parameters
  const { data, isLoading, error, refetch } = useDataFetch({
    queryKey: 'diary-entries',
    endPoint: 'diary/entries',
    params: {
      page: currentPage,
      limit: 10,
    },
  });

  const entries = data?.items ?? [];
  const paginationMeta = {
    totalCount: data?.totalCount ?? 0,
    totalItems: data?.totalItems ?? 0,
    itemsPerPage: data?.itemsPerPage ?? 10,
    currentPage: data?.currentPage ?? 1,
    totalPages: data?.totalPages ?? 1,
  };
  const hasEntries = entries.length > 0;
  const isSinglePage = isOpen && currentIndex + 1 >= entries.length;

  // No need for localStorage persistence - Redux persist handles this automatically

  // Validate and adjust currentIndex when entries change
  useEffect(() => {
    if (entries.length > 0 && currentIndex >= entries.length) {
      // If current index is beyond available entries, reset to last valid index
      const maxIndex = Math.max(0, entries.length - 1);
      dispatch(setMyDiaryCurrentIndex(maxIndex));
    }
  }, [entries.length, currentIndex, dispatch]);

  // Reset currentIndex when page changes (new data is loaded)
  useEffect(() => {
    if (entries.length > 0) {
      // When new page data is loaded, ensure currentIndex is valid for the new page
      const maxIndexForPage = Math.max(0, entries.length - 1);
      if (currentIndex > maxIndexForPage) {
        dispatch(setMyDiaryCurrentIndex(0));
      }
    }
  }, [currentPage, entries.length, currentIndex, dispatch]);

  // Handle state validation after data is loaded
  useEffect(() => {
    if (entries.length > 0 && !isLoading) {
      // Ensure the current index is within bounds for the loaded entries
      if (currentIndex >= entries.length) {
        dispatch(setMyDiaryCurrentIndex(0));
      }
    }
  }, [entries.length, isLoading, currentIndex, isOpen, currentPage, dispatch]);

  // Track initial load completion
  useEffect(() => {
    if (!isLoading && data && !hasInitiallyLoaded) {
      setHasInitiallyLoaded(true);
    }
  }, [isLoading, data, hasInitiallyLoaded]);

  const openDiary = useCallback(() => {
    if (hasEntries) {
      // Only reset to 0 if this is a fresh open
      if (!isOpen) {
        dispatch(setMyDiaryCurrentIndex(0));
      }
      dispatch(setMyDiaryIsOpen(true));
    }
  }, [hasEntries, isOpen, dispatch]);

  const closeDiary = () => {
    dispatch(setMyDiaryIsOpen(false));
    // Optionally reset state when diary is explicitly closed
    // dispatch(resetMyDiaryState());
  };

  const goLeft = () => {
    const step = isMobile ? 1 : 2; // Mobile: 1 entry, Desktop: 2 entries

    // Check if we're at the beginning of current page
    if (currentIndex === 0) {
      // If we're on page 1, can't go further left
      if (currentPage === 1) {
        return;
      }
      // Go to previous page and set index to last item of that page
      dispatch(setMyDiaryCurrentPage(currentPage - 1));
      const lastIndexOfPreviousPage = isMobile
        ? paginationMeta.itemsPerPage - 1
        : paginationMeta.itemsPerPage - 2;
      dispatch(setMyDiaryCurrentIndex(Math.max(lastIndexOfPreviousPage, 0)));
    } else {
      // Navigate within current page
      dispatch(setMyDiaryCurrentIndex(Math.max(currentIndex - step, 0)));
    }
  };

  const goRight = () => {
    if (!isOpen && hasEntries) {
      openDiary();
      return;
    }

    const step = isMobile ? 1 : 2; // Mobile: 1 entry, Desktop: 2 entries
    const nextIndex = currentIndex + step;

    // Check if we're at the end of current page entries
    if (nextIndex >= entries.length) {
      // If we're on the last page, can't go further right
      if (currentPage >= paginationMeta.totalPages) {
        return;
      }
      // Go to next page and reset index to 0
      dispatch(setMyDiaryCurrentPage(currentPage + 1));
      dispatch(setMyDiaryCurrentIndex(0));
    } else {
      // Navigate within current page
      dispatch(setMyDiaryCurrentIndex(nextIndex));
    }
  };

  // Function to navigate to a specific page (useful for direct navigation)
  const goToPage = useCallback(
    (pageNumber) => {
      if (pageNumber >= 1 && pageNumber <= paginationMeta.totalPages) {
        dispatch(setMyDiaryCurrentPage(pageNumber));
        dispatch(setMyDiaryCurrentIndex(0)); // Reset to first entry of the page
        if (!isOpen && hasEntries) {
          dispatch(setMyDiaryIsOpen(true));
        }
      }
    },
    [paginationMeta.totalPages, hasEntries, isOpen, dispatch]
  );

  const handleCoverPhotoChange = useCallback(
    (url) => {
      dispatch(setMyDiaryCoverPhotoUrl(url));
    },
    [dispatch]
  );

  const backgroundStyle = useMemo(
    () =>
      coverPhotoUrl && !isOpen
        ? {
            backgroundImage: `url(${coverPhotoUrl})`,
            backgroundSize: 'contain',
            backgroundPosition: 'center',
            backgroundRepeat: 'no-repeat',
          }
        : {},
    [coverPhotoUrl]
  );


  if (error) {
    return (
      <div className="flex justify-center min-h-[calc(100vh-180px)] max-sm:min-h-screen items-center">
        <div className="text-center text-red-500">
          <p className="text-lg">Error loading diary entries</p>
          <p className="text-sm">{error.message}</p>
        </div>
      </div>
    );
  }

  return (
    <div className="flex justify-center min-h-[calc(100vh-200px)] p-4 xl:mt-4">
      <div
        className={`relative ${
          isOpen
            ? 'w-full max-w-[900px]'
            : 'w-full max-w-[647px]'
        } ${isSinglePage ? 'min-h-[600px] max-h-[calc(100vh-180px)]' : 'min-h-[600px] max-h-[800px]'} ${
          !isOpen && coverPhotoUrl ? '' : 'bg-[#FDE7E9]'
        } rounded-lg shadow-lg border bg-yellow-100 border-gray-300 transition-all duration-300`}
        style={backgroundStyle}
      >
        {(isOpen || isLoading) ? (
          <>
            <DiaryContent
              entries={entries}
              currentIndex={currentIndex}
              isMobile={isMobile}
              isLoading={isLoading}
              isInitialLoad={!hasInitiallyLoaded}
            />
            <div className="absolute lg:right-[-60px] right-0 -top-14 lg:top-4 z-10 flex lg:flex-col flex-row-reverse items-center">
              <ButtonIcon
                icon="mdi:close"
                innerBtnCls="h-10 w-10 cursor-pointer"
                btnIconCls="h-5 w-5"
                aria-label="close diary"
                onClick={closeDiary}
              />
              {/* Calendar button for open diary view - below close button */}
              <div className="lg:mt-2 relative">
                <ButtonIcon
                  icon="material-symbols:calendar-month-outline"
                  innerBtnCls="max-sm:h-12 max-sm:w-12 h-14 w-14 cursor-pointer"
                  btnIconCls="h-5 max-sm:h-4 w-5 max-sm:w-4"
                  aria-label="open calendar"
                  onClick={() => setShowCalendar(!showCalendar)}
                />

                {/* Calendar Filter Modal */}
                {showCalendar && (
                  <div
                    className={
                      showCalendar
                        ? 'block absolute max-w-xl min-w-80 max-h-[400px] max-sm:max-h-[300px] overflow-y-auto bg-white border rounded-lg shadow-lg z-20 mt-8 top-0 right-0'
                        : 'hidden'
                    }
                  >
                    <CalendarFilter
                      onDateSelect={() => {
                        // The CalendarFilter component will update the Redux store
                        // This callback is for any additional actions needed
                        if (fetchTodayEntry) {
                          fetchTodayEntry();
                        }
                      }}
                      onClose={() => setShowCalendar(false)}
                    />
                  </div>
                )}
              </div>
              {/* <DiaryIconsSidebar className="mt-20" showSkin={false} /> */}
            </div>
          </>
        ) : (
          <>
            <DiaryCover
              hasEntries={hasEntries}
              onOpen={openDiary}
              onCoverPhotoChange={handleCoverPhotoChange}
            />
          </>
        )}
        <DiaryPagination
          hasEntries={hasEntries}
          isOpen={isOpen}
          currentIndex={currentIndex}
          totalEntries={paginationMeta?.totalItems}
          currentPage={paginationMeta?.currentPage}
          totalPages={paginationMeta?.totalPages}
          itemsPerPage={paginationMeta?.itemsPerPage}
          isMobile={isMobile}
          onLeftClick={goLeft}
          onRightClick={goRight}
        />
      </div>
    </div>
  );
}
