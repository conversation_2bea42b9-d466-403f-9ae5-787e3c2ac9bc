import { useDispatch, useSelector } from 'react-redux';
import { toast } from 'sonner';
import api from '@/lib/api';
import {
  setIsSubmitting,
  setSubmitResult,
  setGameCompleted,
  setError,
  setSubmissionInitiated,
  selectIsSubmitting,
  selectGameCompleted,
  selectUserAnswers,
  selectSetId,
  selectSubmissionInitiated,
  selectCurrentQuestionIndex,
  selectQuestions,
  selectBlanks,
  selectHasChecked,
} from '@/store/features/waterfallSlice';
import { createAnswerData, createSimpleAnswerData, updateAnswersArray } from '../utils/answerUtils';

export const useWaterfallSubmission = () => {
  const dispatch = useDispatch();
  
  const isSubmitting = useSelector(selectIsSubmitting);
  const gameCompleted = useSelector(selectGameCompleted);
  const userAnswers = useSelector(selectUserAnswers);
  const setId = useSelector(selectSetId);
  const submissionInitiated = useSelector(selectSubmissionInitiated);
  const currentQuestionIndex = useSelector(selectCurrentQuestionIndex);
  const questions = useSelector(selectQuestions);
  const blanks = useSelector(selectBlanks);
  const hasChecked = useSelector(selectHasChecked);

  // Handle submit answers
  const handleSubmitAnswers = async (answersToSubmit = null) => {
    // Prevent duplicate submissions
    if (isSubmitting || gameCompleted) return;

    const finalAnswers = answersToSubmit || userAnswers;

    if (!finalAnswers || finalAnswers.length === 0) {
      toast.error('No answers to submit');
      return;
    }

    // Convert complex answer data to simple format for API
    const simpleAnswers = finalAnswers.map(answer => ({
      question_id: answer.question_id,
      answers: answer.answers
    }));

    dispatch(setIsSubmitting(true));
    dispatch(setError(null));

    try {
      const response = await api.post('/play/waterfall/submit', {
        set_id: setId,
        answers: simpleAnswers,
      });

      if (response.data) {
        dispatch(setSubmitResult(response.data));
        dispatch(setGameCompleted(true));
      } else {
        throw new Error('Invalid response from server');
      }
    } catch (error) {
      console.error('Submission error:', error);
      const errorMessage = error.response?.data?.message || 'Failed to submit answers';
      dispatch(setError(errorMessage));
    } finally {
      dispatch(setIsSubmitting(false));
    }
  };

  // Handle next question with validation and submission logic
  const handleNextQuestion = () => {
    // Check if any blanks are filled
    const hasAnyAnswer = blanks.some((blank) => blank !== null);

    // Always require at least one answer for all questions
    if (!hasAnyAnswer) {
      toast.error('Please complete the sentences to check.');
      return;
    }

    // If answers are filled but not checked, check them first
    if (!hasChecked) {
      // This should trigger the check answers flow
      return 'CHECK_ANSWERS_FIRST';
    }

    // Move to next question (never submit from here)
    return proceedToNextQuestion();
  };

  // Proceed to next question - never submit from here
  const proceedToNextQuestion = () => {
    if (currentQuestionIndex < questions.length - 1) {
      // Move to next question
      return 'NEXT_QUESTION';
    } else {
      // Last question - this should never submit, only move to next
      // Submission should only happen from AnswerCheckModal
      return 'LAST_QUESTION_REACHED';
    }
  };

  // Submit game - only called from modals
  const submitGame = () => {
    const currentQuestion = questions[currentQuestionIndex];
    const hasFilledBlanks = blanks.some((blank) => blank !== null);

    // If there are filled blanks and not yet saved, save them first
    if (hasFilledBlanks && !hasChecked) {
      const answerData = createAnswerData(currentQuestion, blanks);
      if (answerData) {
        const updatedAnswers = updateAnswersArray(userAnswers, answerData);
        // Submit with the updated answers including the last question
        handleSubmitAnswers(updatedAnswers);
        return 'SUBMITTING_WITH_LAST_ANSWER';
      }
    }

    // If no blanks are filled, create empty answer data for the last question
    if (!hasFilledBlanks && !hasChecked) {
      const answerData = createAnswerData(currentQuestion, blanks); // This will create empty answers
      if (answerData) {
        const updatedAnswers = updateAnswersArray(userAnswers, answerData);
        // Submit with the empty answer for the last question
        handleSubmitAnswers(updatedAnswers);
        return 'SUBMITTING_WITH_EMPTY_ANSWER';
      }
    }

    // Prevent duplicate submissions
    if (!submissionInitiated && !isSubmitting && !gameCompleted) {
      dispatch(setSubmissionInitiated(true));
      handleSubmitAnswers();
      return 'SUBMITTING';
    }

    return 'ALREADY_SUBMITTED';
  };

  return {
    // State
    isSubmitting,
    gameCompleted,
    submissionInitiated,

    // Actions
    handleSubmitAnswers,
    handleNextQuestion,
    proceedToNextQuestion,
    submitGame,
  };
};
