'use client';
import React, { useState } from 'react';
import Image from 'next/image';
import { Icon } from '@iconify/react';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import useDataFetch from '@/hooks/useDataFetch';
import SkinPreview from '../skin/SkinPreview';
import { usePathname, useRouter } from 'next/navigation';
import api from '@/lib/api';

const SharedDiarySection = () => {
  const [currentPage, setCurrentPage] = useState(1);
  const [limit, setLimit] = useState(10);

  const { data, isLoading, error } = useDataFetch({
    queryKey: ['shared-entries', currentPage, limit],
    endPoint: 'diary/shared-entries',
    params: { page: currentPage, limit: limit, sortBy: 'createdAt' },
  });
  const router = useRouter();
  const queryClient = useQueryClient();
  const pathname = usePathname();

  // Like/Unlike mutation
  const likeMutation = useMutation({
    mutationFn: async ({ entryId, isLiked }) => {
      if (isLiked) {
        // If already liked, call DELETE to unlike
        return await api.delete(`/diary/entries/${entryId}/like`);
      } else {
        // If not liked, call POST to like
        return await api.post(`/diary/entries/${entryId}/like`);
      }
    },
    onSuccess: () => {
      // Invalidate and refetch shared entries to get updated like status
      queryClient.invalidateQueries({
        queryKey: ['shared-entries', currentPage, limit],
      });
    },
    onError: (error) => {
      console.error('Error updating like status:', error);
    },
  });

  // Handle like/unlike functionality
  const handleLike = (entryId) => {
    // Find the entry to check its current like status
    const entry = sharedEntries.find((entry) => entry.id === entryId);
    if (entry) {
      likeMutation.mutate({ entryId, isLiked: entry.hasLiked });
    }
  };

  // Handle navigation to diary details
  const handleViewDiary = (entryId) => {
    router.push(`/diary/shared/${entryId}`);
  };

  const sharedEntries = data?.items || [];
  const totalCount = data?.totalCount || data?.totalItems || 0;
  const totalPages = data?.totalPages || Math.ceil(totalCount / limit);
  const itemsPerPage = data?.itemsPerPage || limit;

  // console.log('Shared entries:', sharedEntries);
  return (
    <div
      className={
        pathname === '/' ? 'max-w-7xl mx-auto p-5 xl:px-0 my-6' : 'lg:px-10'
      }
    >
      <div className="mb-2 w-full text-[30px] font-semibold text-[#432005]">
        Shared Diary
      </div>

      {/* Loading State */}
      {isLoading ? (
        <div className="flex justify-center items-center py-20">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-yellow-500"></div>
        </div>
      ) : error ? (
        <div className="text-center py-20">
          <p className="text-red-500 text-lg">Error loading shared diaries</p>
        </div>
      ) : (
        <>
          {/* Shared Diaries Grid */}
          <div
            className={`grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6`}
          >
            {sharedEntries.length > 0 ? (
              sharedEntries.map((entry, index) => (
                <div
                  key={index}
                  className="bg-white rounded-lg shadow-md overflow-hidden"
                >
                  <SkinPreview
                    key={index}
                    skin={entry.skin.templateContent}
                    contentData={{
                      subject: entry.title,
                      body: entry.content,
                      date: entry.entryDate,
                    }}
                  />
                  {/* User info section with profile design */}
                  <div
                    className="p-4 bg-gradient-to-br from-[#FFF8F0] to-[#F5E6D3] relative"
                    style={{
                      boxShadow: `
                  2px 2px 12px 0px #F5D1B066 inset,
                  -2px -2px 12px 0px #F5D1B066 inset
                `,
                    }}
                  >
                    <div className="flex items-center justify-between gap-1">
                      {/* Left side - Profile info */}
                      <div className="flex items-center space-x-3">
                        {/* Avatar */}
                        <div className="min-w-6 max-w-12 h-102 rounded-full overflow-hidden bg-gray-200">
                          <Image
                            src={
                              entry.user?.profilePicture ||
                              '/assets/images/all-img/avatar.png'
                            }
                            alt={entry.user?.name || 'User'}
                            width={54}
                            height={54}
                            className="w-full max-sm:max-w-8 max-sm:max-h-8 h-full object-cover"
                          />
                        </div>

                        {/* Name and likes */}
                        <div>
                          <div className="text-sm text-gray-600">Name</div>
                          <div className="text-sm sm:text-lg font-semibold text-[#432005] mb-2">
                            {entry.diary.user?.name || 'User Name'}
                          </div>
                        </div>
                      </div>

                      {/* Right side - View Diary button */}
                      <div className="space-y-1">
                        {/* Likes section */}
                        <div
                          className="flex items-center justify-end space-x-1 cursor-pointer"
                          onClick={() => handleLike(entry.id)}
                        >
                          <Icon
                            icon={
                              entry.hasLiked ? 'mdi:like' : 'mdi:like-outline'
                            }
                            className={`text-2xl transition-colors duration-200 ${
                              entry.hasLiked
                                ? 'text-[#CC8A02]'
                                : 'text-[#432005]'
                            }`}
                          />
                          <span className="text-xl font-bold text-[#432005]">
                            {entry.likeCount}
                          </span>
                        </div>
                        <button
                          onClick={() => handleViewDiary(entry.id)}
                          className="bg-gradient-to-br from-[#FFF8E1] to-[#F5E6A3] px-4 py-2 xl:py-0 2xl:px-6 text-xs 2xl:text-sm rounded-full font-semibold text-[#432005] border border-[#723F11] hover:shadow-lg transition-all duration-200 flex items-center space-x-2"
                          style={{
                            boxShadow: `
                      2px 2px 8px 0px rgba(0,0,0,0.1),
                      inset 1px 1px 3px 0px rgba(255,255,255,0.8)
                    `,
                          }}
                        >
                          <span>View Diary</span>
                          <Image
                            src="/assets/images/all-img/Group.svg"
                            alt="Close"
                            width={40}
                            height={40}
                            className="mt-1 hidden xl:block"
                          />
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              ))
            ) : (
              <div className="text-center py-20">
                <p className="text-gray-500 text-lg">
                  No shared diaries available
                </p>
              </div>
            )}
          </div>

          {/* Pagination Controls */}
          {totalCount > 0 && pathname !== '/' && (
            <div className="flex items-center justify-between max-sm:justify-center py-6 bg-white rounded-lg">
              {/* Left: Record count */}
              <div className="max-sm:hidden text-sm text-gray-700">
                Showing {(currentPage - 1) * itemsPerPage + 1}-
                {Math.min(currentPage * itemsPerPage, totalCount)} of{' '}
                {totalCount} diaries
              </div>

              {/* Center: Pagination */}
              <div className="flex items-center space-x-1">
                <button
                  onClick={() => setCurrentPage(currentPage - 1)}
                  disabled={currentPage === 1}
                  className="p-2 rounded hover:bg-gray-100 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  <Icon icon="mdi:chevron-left" className="w-5 h-5" />
                </button>

                {/* Dynamic pagination buttons */}
                {totalPages <= 5 ? (
                  // If 5 or fewer pages, show all
                  Array.from({ length: totalPages }, (_, i) => (
                    <button
                      key={i}
                      onClick={() => setCurrentPage(i + 1)}
                      className={`px-3 py-1 rounded ${
                        currentPage === i + 1
                          ? 'bg-yellow-400 text-white font-medium'
                          : 'hover:bg-gray-100'
                      }`}
                    >
                      {i + 1}
                    </button>
                  ))
                ) : (
                  // If more than 5 pages, show smart pagination
                  <>
                    {/* First page */}
                    <button
                      onClick={() => setCurrentPage(1)}
                      className={`px-3 py-1 rounded ${
                        currentPage === 1
                          ? 'bg-yellow-400 text-white font-medium'
                          : 'hover:bg-gray-100'
                      }`}
                    >
                      1
                    </button>

                    {/* Ellipsis for first gap if needed */}
                    {currentPage > 3 && <span className="px-2">...</span>}

                    {/* Pages around current page */}
                    {Array.from({ length: Math.min(3, totalPages) }, (_, i) => {
                      let pageNum;
                      if (currentPage <= 2) {
                        pageNum = i + 2;
                      } else if (currentPage >= totalPages - 1) {
                        pageNum = totalPages - 3 + i;
                      } else {
                        pageNum = currentPage - 1 + i;
                      }

                      if (pageNum > 1 && pageNum < totalPages) {
                        return (
                          <button
                            key={i}
                            onClick={() => setCurrentPage(pageNum)}
                            className={`px-3 py-1 rounded ${
                              currentPage === pageNum
                                ? 'bg-yellow-400 text-white font-medium'
                                : 'hover:bg-gray-100'
                            }`}
                          >
                            {pageNum}
                          </button>
                        );
                      }
                      return null;
                    }).filter(Boolean)}

                    {/* Ellipsis for last gap if needed */}
                    {currentPage < totalPages - 2 && (
                      <span className="px-2">...</span>
                    )}

                    {/* Last page */}
                    {totalPages > 1 && (
                      <button
                        onClick={() => setCurrentPage(totalPages)}
                        className={`px-3 py-1 rounded ${
                          currentPage === totalPages
                            ? 'bg-yellow-400 text-white font-medium'
                            : 'hover:bg-gray-100'
                        }`}
                      >
                        {totalPages}
                      </button>
                    )}
                  </>
                )}

                <button
                  onClick={() => setCurrentPage(currentPage + 1)}
                  disabled={currentPage === totalPages}
                  className="p-2 rounded hover:bg-gray-100 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  <Icon icon="mdi:chevron-right" className="w-5 h-5" />
                </button>
              </div>

              {/* Right: Items per page */}
              <div className="flex items-center max-sm:hidden">
                <span className="text-sm text-gray-700 mr-2">
                  Items per page:
                </span>
                <select
                  className="border border-gray-300 rounded px-2 py-1 text-sm"
                  value={limit}
                  onChange={(e) => {
                    setLimit(Number(e.target.value));
                    setCurrentPage(1); // Reset to first page when changing limit
                  }}
                >
                  <option value="10">10</option>
                  <option value="25">25</option>
                  <option value="50">50</option>
                  <option value="100">100</option>
                </select>
              </div>
            </div>
          )}
        </>
      )}
    </div>
  );
};

export default SharedDiarySection;
