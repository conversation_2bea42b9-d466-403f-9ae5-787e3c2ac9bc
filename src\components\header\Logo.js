import React from 'react';
import Image from 'next/image';
import Link from 'next/link';

const Logo = ({ isAuth, isStudent }) => {
  return (
    <Link
      href={
        isAuth && !isStudent
          ? '/dashboard'
          : isAuth
          ? '/diary'
          : '/'
      }
    >
      <Image
        src="/assets/images/all-img/Logo.png"
        alt="icon"
        width={120}
        height={80}
        priority
        className="cursor-pointer max-w-24"
      />
    </Link>
  );
};

export default Logo;
