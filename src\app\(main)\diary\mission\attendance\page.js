'use client';
import React, { useMemo, useState } from 'react';
import { Icon } from '@iconify/react';
import useDataFetch from '@/hooks/useDataFetch';
import FullCalendar from '@fullcalendar/react';
import dayGridPlugin from '@fullcalendar/daygrid';
import interactionPlugin from '@fullcalendar/interaction';
import { useSelector } from 'react-redux';
import { formatDate } from '@/utils/dateFormatter';

// Utility function to format date consistently (avoids timezone issues)
const formatDateToString = (date) => {
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  return `${year}-${month}-${day}`;
};

const AttendancePage = () => {
  // State for date filters
  const { user } = useSelector((state) => state.auth);

  const [selectedEntry, setSelectedEntry] = useState(null);
  const [modalOpen, setModalOpen] = useState(false);

  const handleDateClick = (arg) => {
    // Handle both dateClick (from calendar) and eventClick (from event content)
    let dateStr;
    let selectedAttendance = null;

    if (arg.dateStr) {
      // Called from dateClick - this is already in correct format
      dateStr = arg.dateStr;
      selectedAttendance = attendanceMap[dateStr];
    } else if (arg.event?.start) {
      // Called from eventContent click - fix timezone issue
      const eventDate = arg.event.start;

      // Use local date instead of UTC to avoid timezone shift
      dateStr = formatDateToString(eventDate);

      selectedAttendance = attendanceMap[dateStr];
    } else {
      console.error('Invalid arg passed to handleDateClick:', arg);
      return;
    }

    // Check if it's a future date
    const clickedDate = new Date(dateStr);
    clickedDate.setHours(0, 0, 0, 0);

    const today = new Date();
    today.setHours(0, 0, 0, 0);

    if (clickedDate > today) {
      // Don't show modal for future dates
      return;
    }

    // Debug: Show all items with 'present' status
    const presentItems = Object.values(attendanceMap).filter(
      (item) => item.status === 'present'
    );

    // Pass the exact object to the state
    if (selectedAttendance) {
      setSelectedEntry(selectedAttendance);
    } else {
      // Manual search through all items
      const manualMatch = Object.values(attendanceMap).find((item) => {
        return item.entryDate === dateStr;
      });

      if (manualMatch) {
        setSelectedEntry(manualMatch);
      } else {
        // Create a minimal object for dates with no data
        setSelectedEntry({
          entryDate: dateStr,
          status: 'absent',
          diaryTitle: 'No diary entry',
          progress: 0,
          wordCount: 0,
        });
      }
    }

    setModalOpen(true);
  };

  // Fetch attendance data from API
  const {
    data: attendanceResponse,
    isLoading,
    error,
    refetch,
  } = useDataFetch({
    queryKey: ['diary-attendance'],
    endPoint: '/diary-attendance/student',
  });

  // Safely create the map only if data is available
  const attendanceMap = useMemo(() => {
    if (!attendanceResponse) {
      return {};
    }

    const map = attendanceResponse.reduce((map, item) => {
      map[item.entryDate] = item;
      return map;
    }, {});

    return map;
  }, [attendanceResponse]);

  // Only get events if user and attendanceMap are ready
  const events = useMemo(() => {
    if (!user?.createdAt || !attendanceMap) {
      return [];
    }

    const start = new Date(user.createdAt);
    start.setHours(0, 0, 0, 0); // Normalize to midnight
    let dates = [];

    const today = new Date();
    today.setHours(0, 0, 0, 0); // Normalize to midnight

    for (let d = new Date(start); d <= today; d.setDate(d.getDate() + 1)) {
      // Ensure d is always at midnight
      d.setHours(0, 0, 0, 0);

      const dateStr = formatDateToString(d);

      const entry = attendanceMap[dateStr];
      const status = entry?.status || 'absent';

      dates.push({
        title: status === 'present' ? 'Present' : 'Absent',
        progress: `(Completed ${Math.round((entry?.progress || 0) * 100)}%)`,
        date: dateStr,
        backgroundColor: status === 'present' ? '#4ade80' : '#ff8383ff',
        extendedProps: {
          originalEntry: entry,
          progress: entry?.progress || 0,
        },
      });
    }

    return dates;
  }, [user?.createdAt, attendanceMap]);

  return (
    <div className="w-full max-w-full p-4 overflow-hidden">
      <style jsx global>{`
        .attendance-calendar-container {
          max-width: 100vw !important;
          overflow: hidden !important;
        }
        .attendance-calendar-container .fc {
          max-width: 100% !important;
          overflow: hidden !important;
        }
        .attendance-calendar-container .fc .fc-daygrid-day {
          height: auto !important;
          min-height: 60px !important;
          max-height: 80px !important;
        }
        .attendance-calendar-container .fc .fc-daygrid-day-events {
          display: block !important;
        }
        .attendance-calendar-container .fc .fc-daygrid-event {
          font-size: 0.9rem !important;
          padding: 5px 2px !important;
          margin: 1px 0 !important;
          border-radius: 3px !important;
          overflow: hidden !important;
          max-width: 70% !important;
          margin: 1px auto !important;
          text-align: center !important;
          line-height: 1.1 !important;
          min-height: 20px !important;
        }
        .attendance-calendar-container .fc .fc-scrollgrid {
          overflow: hidden !important;
        }
        .attendance-calendar-container .fc .fc-scrollgrid-sync-table {
          width: 100% !important;
        }
        @media (max-width: 768px) {
          .attendance-calendar-container .fc .fc-daygrid-day {
            min-height: 50px !important;
            max-height: 70px !important;
          }
          .attendance-calendar-container .fc .fc-daygrid-event {
            font-size: 0.6rem !important;
            min-height: 18px !important;
          }
        }
        @media (max-width: 640px) {
          .attendance-calendar-container .fc .fc-daygrid-day {
            min-height: 45px !important;
            max-height: 60px !important;
          }
          .attendance-calendar-container .fc .fc-daygrid-event {
            font-size: 0.55rem !important;
            min-height: 16px !important;
          }
        }
      `}</style>
      <div className="attendance-calendar-container">
        <FullCalendar
          plugins={[dayGridPlugin, interactionPlugin]}
          initialView="dayGridMonth"
          height="auto"
          contentHeight="auto"
          aspectRatio={1.35}
          events={events}
          eventContent={(arg) => {
            const status = arg.event?.title || '';
            const progress = arg.event?.extendedProps?.progress || '';
            const tooltipText = `${status}${progress ? ` - ${progress}` : ''}`;

            return (
              <div className="text-center cursor-pointer" title={tooltipText}>
                <div className="font-medium">{status}</div>
                <div className="text-xs">{progress}</div>
              </div>
            );
          }}
          eventClick={handleDateClick}
          dateClick={handleDateClick}
          dayCellClassNames={(arg) => {
            const today = new Date();
            today.setHours(0, 0, 0, 0);
            const cellDate = new Date(arg.date);

            return cellDate <= today
              ? 'cursor-pointer'
              : 'pointer-events-none opacity-75';
          }}
        />
      </div>

      {modalOpen && selectedEntry && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
          <div className="bg-white p-4 rounded-lg max-w-2xl m-3 min-w-md shadow-lg space-y-4">
            {/* Header */}
            <div className="flex items-center justify-between border-b pb-2">
              <h3 className="text-lg font-semibold">
                Attendance for {formatDate(selectedEntry.entryDate, 'short')}
              </h3>
              <p
                className={`text-sm font-semibold px-2 py-1 rounded ${
                  selectedEntry?.status === 'present'
                    ? 'text-green-700 bg-green-100'
                    : 'text-red-700 bg-red-100'
                }`}
              >
                {selectedEntry?.status?.toUpperCase() || 'ABSENT'}
              </p>
            </div>

            {/* Diary Information */}
            {selectedEntry?.diaryTitle || selectedEntry?.diaryEntry ? (
              <div className="space-y-3">
                <div>
                  <p className="text-sm font-semibold text-gray-600">Title:</p>
                  <p className="text-base">
                    {selectedEntry?.diaryTitle ||
                      selectedEntry?.diaryEntry?.title ||
                      'No title'}
                  </p>
                </div>

                {selectedEntry?.diaryEntry?.content && (
                  <div>
                    <p className="text-sm font-semibold text-gray-600">
                      Content:
                    </p>
                    <div className="bg-gray-50 p-3 rounded text-sm max-h-32 overflow-y-auto">
                      {selectedEntry.diaryEntry.content}
                    </div>
                  </div>
                )}

                {/* Attendance Details */}
                <div className="grid grid-cols-2 sm:grid-cols-3 gap-4">
                  <div>
                    <p className="text-sm font-semibold text-gray-600">
                      Word Count:
                    </p>
                    <p>{selectedEntry?.wordCount || 0}</p>
                  </div>
                  <div>
                    <p className="text-sm font-semibold text-gray-600">
                      Progress:
                    </p>
                    <p>
                      {selectedEntry?.progress
                        ? `${(selectedEntry.progress * 100).toFixed(1)}%`
                        : '0%'}
                    </p>
                  </div>
                  <div>
                    <p className="text-sm font-semibold text-gray-600">
                      Diary Status:
                    </p>
                    <p className="text-sm capitalize">
                      {selectedEntry?.diaryEntry?.status || '--'}
                    </p>
                  </div>
                </div>
              </div>
            ) : (
              <div className="text-center py-4">
                <p className="text-gray-500">
                  No diary entry submitted for this day.
                </p>
              </div>
            )}

            {/* Close Button */}
            <div className="flex justify-end pt-3 border-t">
              <button
                className="bg-yellow-500 hover:bg-yellow-600 text-white px-6 py-2 rounded transition-colors"
                onClick={() => setModalOpen(false)}
              >
                Close
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default AttendancePage;
