import { ButtonIcon } from '@/components/Button';
import GoBack from '@/components/shared/GoBack';
import Image from 'next/image';
import Link from 'next/link';
import React from 'react';

const Waterfall = () => {
  return (
    <div className="max-w-7xl mx-auto px-5 mb-8 xl:px-0">
      <GoBack title={'HEC Waterfall Play'} linkClass="my-5 w-full max-w-60" />

      <div className="p-4 xl:px-6 mb-3 bg-[#FFF6EF] flex flex-wrap items-center justify-between rounded-lg shadow-lg text-center gap-5">
        <Image
          src={'/assets/images/all-img/catImg.png'}
          alt={'waterfall'}
          width={300}
          height={500}
          className="max-w-[200px] mx-auto sm:max-w-[250px] h-auto"
        />

        <div className="space-y-2 sm:space-y-3 mx-auto">
          <p className="text-xl text-yellow-600">Take me to</p>

          <h1 className="text-3xl sm:text-5xl font-semibold bg-gradient-to-b from-yellow-500 to-yellow-700 text-transparent bg-clip-text">
            Waterfall
          </h1>
          <p className="text-md text-yellow-600">
            Catch and drag to make the perfect sentence! Let’s have some fun!
          </p>
        </div>

        <Link
          href={'/waterfall/play'}
          className="flex items-center mx-auto gap-2 border border-yellow-800 text-xl sm:text-3xl text-yellow-900 bg-gradient-to-b from-[#DCA600] to-[#FFDE5B] px-10 py-1 rounded-full mt-3"
        >
          Let's Play{' '}
          <ButtonIcon
            icon={'famicons:play'}
            innerBtnCls={'h-10 w-10 sm:h-14 sm:w-14'}
            btnIconCls={'sm:h-6 sm:w-6 h-3 w-3 text-white'}

          />
        </Link>
      </div>
    </div>
  );
};

export default Waterfall;
