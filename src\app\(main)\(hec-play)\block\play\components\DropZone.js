import React, { useEffect, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';

const DropZone = ({
  word = null,
  index,
  blockIndex,
  hide = false,
  disable = false,
  disableDrop = false,
  disableRemove = false,
  dragType,
  onDrop,
  onRemoveWord,
  onClick,
  isMobile = false,
  selectedWord = null,
  className = "",
  style = {}
}) => {
  const dropZoneRef = useRef(null);

  if (hide) return null;

  const handleDragOver = (e) => {
    if (disable || disableDrop) {
      return;
    }
    e.preventDefault();
  };

  const handleDrop = (e) => {
    if (disable || disableDrop) {
      e.preventDefault();
      return;
    }
    if (onDrop) {
      onDrop(dragType, index, blockIndex);
    }
  };

  const handleRemoveWord = () => {
    if (disable || disableRemove || !word) {
      return;
    }
    if (onRemoveWord) {
      onRemoveWord(index, dragType, blockIndex);
    }
  };

  // Handle mobile drop events
  useEffect(() => {
    const dropZone = dropZoneRef.current;
    if (!dropZone) return;

    const handleMobileDrop = (e) => {
      if (disable || disableDrop) return;

      const { draggedWord, dragType: sourceDragType } = e.detail;
      if (sourceDragType === dragType && onDrop) {
        onDrop(dragType, index, blockIndex);
      }
    };

    dropZone.addEventListener('mobileDrop', handleMobileDrop);
    return () => {
      dropZone.removeEventListener('mobileDrop', handleMobileDrop);
    };
  }, [disable, disableDrop, dragType, index, blockIndex, onDrop]);

  const getClassName = () => {
    let baseClass = `px-2 ${word ? 'py-0.5': 'py-2'} sm:px-4 bg-gradient-to-br from-yellow-50 to-amber-50 border sm:border-2 border-dashed border-yellow-400 rounded-xl flex items-center justify-center text-xs sm:text-sm font-semibold transition-all duration-300 shadow-sm hover:shadow-md`;

    if (disable) {
      baseClass += " opacity-50";
    }

    if (isMobile && selectedWord && !word) {
      baseClass += " ring-2 ring-yellow-500 bg-gradient-to-br from-yellow-100 to-amber-100 cursor-pointer scale-105 shadow-lg";
    } else if (isMobile && !word) {
      baseClass += " cursor-pointer hover:bg-gradient-to-br hover:from-yellow-100 hover:to-amber-100 hover:scale-105";
    }

    if (!word) {
      baseClass += " hover:border-yellow-500 hover:shadow-lg";
    }

    if (className) {
      baseClass += ` ${className}`;
    }

    return baseClass;
  };

  return (
    <motion.div
      ref={dropZoneRef}
      data-drop-zone="true"
      onDragOver={handleDragOver}
      onDrop={handleDrop}
      onClick={word ? handleRemoveWord : onClick}
      className={getClassName()}
      style={style}
      whileHover={{ scale: 1.02 }}
      whileTap={{ scale: 0.98 }}
      transition={{ duration: 0.2 }}
    >
      <AnimatePresence mode="wait">
        {word ? (
          <motion.span
            key="filled"
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.8 }}
            transition={{ duration: 0.5, ease: "easeInOut" }}
            className="select-none font-bold text-gray-800 tracking-wide flex items-center gap-1"
          >
            <motion.span
              initial={{ opacity: 0, x: -10 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.5, delay: 0.1 }}
              className="text-blue-600"
            >
              {word}
            </motion.span>
            <motion.span
              initial={{ opacity: 0, rotate: -90 }}
              animate={{ opacity: 1, rotate: 0 }}
              transition={{ duration: 0.5, delay: 0.2 }}
              whileHover={{ scale: 1.2, color: "#dc2626" }}
              className="text-red-500 hover:text-red-600 cursor-pointer text-lg"
            >
              ×
            </motion.span>
          </motion.span>
        ) : (
          <motion.span
            key="empty"
            initial={{ opacity: 0, y: 5 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -5 }}
            transition={{ duration: 0.5, ease: "easeInOut" }}
            className="text-gray-500 text-xs font-medium select-none tracking-wide"
          >
            {isMobile && selectedWord ? '✨ Tap to place' : '📍 Drop'}
          </motion.span>
        )}
      </AnimatePresence>
    </motion.div>
  );
};

export default DropZone;
