'use client';
import React from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { Icon } from '@iconify/react';
import {
  undoDecoration,
  redoDecoration,
  selectCanUndo,
  selectCanRedo,
  selectDecorationItems
} from '@/store/features/diarySlice';

const DecorationUndoRedo = ({ className = '' }) => {
  const dispatch = useDispatch();
  const canUndo = useSelector(selectCanUndo);
  const canRedo = useSelector(selectCanRedo);
  const decorationItems = useSelector(selectDecorationItems);



  const handleUndo = () => {
    console.log('Undo button clicked, canUndo:', canUndo);
    if (canUndo) {
      console.log('Dispatching undoDecoration');
      dispatch(undoDecoration());
    }
  };

  const handleRedo = () => {
    console.log('Redo button clicked, canRedo:', canRedo);
    if (canRedo) {
      console.log('Dispatching redoDecoration');
      dispatch(redoDecoration());
    }
  };

  return (
    <div className={`flex items-center gap-1 ${className}`}>
      <button
        onClick={handleUndo}
        disabled={!canUndo}
        className={`p-1.5 cursor-pointer rounded text-xs font-medium transition-colors duration-200 flex items-center justify-center ${
          canUndo
            ? 'bg-white bg-opacity-80 text-gray-700 hover:bg-opacity-100 hover:text-gray-900 shadow-sm border border-gray-200'
            : 'bg-gray-100 text-gray-400 cursor-not-allowed border border-gray-200'
        }`}
        title="Undo (Ctrl+Z)"
        aria-label="Undo decoration"
      >
        <Icon icon="mdi:undo-variant" width={16} height={16} />
      </button>
      <button
        onClick={handleRedo}
        disabled={!canRedo}
        className={`p-1.5 cursor-pointer rounded text-xs font-medium transition-colors duration-200 flex items-center justify-center ${
          canRedo
            ? 'bg-white bg-opacity-80 text-gray-700 hover:bg-opacity-100 hover:text-gray-900 shadow-sm border border-gray-200'
            : 'bg-gray-100 text-gray-400 cursor-not-allowed border border-gray-200'
        }`}
        title="Redo (Ctrl+Y)"
        aria-label="Redo decoration"
      >
        <Icon icon="mdi:redo-variant" width={16} height={16} />
      </button>
    </div>
  );
};

export default DecorationUndoRedo;
