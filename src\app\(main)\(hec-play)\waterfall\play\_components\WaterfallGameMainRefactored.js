'use client';
import React from 'react';
import { DndContext, DragOverlay } from '@dnd-kit/core';
import { motion } from 'framer-motion';

// Components
import MagicalSentenceDisplay from './MagicalSentenceDisplay';
import GameProgressHeader from './GameProgressHeader';
import MagicalWaterfallContainer from './MagicalWaterfallContainer';
import MagicalActionButton from './MagicalActionButton';
import { DragOverlayContent } from './WaterfallGame2';
import GameSummary2 from './GameSummary2';
import TimeExpiredModal from './TimeExpiredModal';
import AnswerCheckModal from './AnswerCheckModal';

// Hooks
import { useWaterfallGame } from './hooks/useWaterfallGame';
import { useWaterfallQuestion } from './hooks/useWaterfallQuestion';
import { useWaterfallDragDrop } from './hooks/useWaterfallDragDrop';
import { useWaterfallModals } from './hooks/useWaterfallModals';
import { useWaterfallSubmission } from './hooks/useWaterfallSubmission';
import { useWaterfallTimer } from './hooks/useWaterfallTimer';

// Redux
import { useSelector } from 'react-redux';
import {
  selectCurrentQuestion,
  selectCurrentTimeLimit,
} from '@/store/features/waterfallSlice';

const WaterfallGameMain = ({ initialData, refetchGame }) => {
  // Custom hooks for game logic
  const gameHook = useWaterfallGame(initialData, refetchGame);
  const questionHook = useWaterfallQuestion();
  const dragDropHook = useWaterfallDragDrop();
  const submissionHook = useWaterfallSubmission();

  // Get current question and timer info from Redux
  const currentQuestion = useSelector(selectCurrentQuestion);
  const currentTimeLimit = useSelector(selectCurrentTimeLimit);
  const hasTimeLimit =
    currentTimeLimit !== null &&
    currentTimeLimit !== undefined &&
    currentTimeLimit > 0;

  // Initialize modals hook first (without timer)
  const modalsHookTemp = useWaterfallModals();

  // Timer hook - integrates with the game state
  const timerHook = useWaterfallTimer(
    hasTimeLimit ? currentTimeLimit : null,
    modalsHookTemp.handleTimeExpired,
    gameHook.currentQuestionIndex
  );

  // Modals hook with timer integration
  const modalsHook = useWaterfallModals(timerHook);

  // Enhanced action handlers that integrate multiple hooks
  const handleCheckAnswers = () => {
    const result = questionHook.handleCheckAnswers();
    if (result.success) {
      modalsHook.showAnswerCheckModalAction();
    }
    return result;
  };

  const handleNextQuestion = () => {
    const result = submissionHook.handleNextQuestion();
    if (result === 'CHECK_ANSWERS_FIRST') {
      return handleCheckAnswers();
    }
    if (result === 'NEXT_QUESTION') {
      return gameHook.proceedToNextQuestion();
    }
    return result;
  };

  // Render current question component
  function renderCurrentQuestion() {
    if (!gameHook.questions.length) return null;

    return (
      <div className="space-y-6">
        {/* Game Progress Header */}
        <div className="flex flex-col lg:flex-row lg:items-center justify-between mb-5 gap-2 relative z-10 max-w-7xl mx-auto">
          <div>
            <motion.h1
              initial={{ opacity: 0, y: -20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, ease: 'easeOut' }}
              className="text- sm:text-2xl font-bold bg-gradient-to-r from-yellow-500 via-yellow-600 to-yellow-500 bg-clip-text text-transparent drop-shadow-sm"
            >
              Catch the Magic Words Falling from the Open Sky! ✨
            </motion.h1>
            <span className="max-sm:text-sm text-yellow-700">
              Playing Question {gameHook?.currentQuestionIndex + 1} of{' '}
              {initialData?.questions?.length}.
            </span>{' '}
          </div>
          <GameProgressHeader
            initialData={initialData}
            currentQuestionIndex={gameHook.currentQuestionIndex}
            timeLeft={timerHook?.timeLeft}
            hasTimeLimit={hasTimeLimit}
            formatTimeDigital={timerHook?.formatTimeDigital}
          />
        </div>

        <MagicalWaterfallContainer
          options={questionHook.options}
          activeId={dragDropHook.activeId}
        />

        {/* Question Display */}

        {/* Waterfall Container */}
        <motion.div
          initial={{ opacity: 0, scale: 0.95 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.6, delay: 0.3 }}
          className="bg-gradient-to-r from-yellow-50 via-orange-50 to-yellow-50 border-2 border-yellow-200 rounded-xl p-4 sm:p-3 sm:pb-5 mb-8 sm:mb-12 shadow-lg  max-w-7xl mx-auto"
        >
          <MagicalSentenceDisplay
            parts={questionHook.getQuestionParts()}
            blanks={questionHook.blanks}
            currentQuestion={questionHook.currentQuestion}
            handleResetBlank={questionHook.handleResetBlank}
          />
        </motion.div>

        {/* Action Button */}
        <MagicalActionButton
          onClick={handleNextQuestion}
          isSubmitting={submissionHook.isSubmitting}
          showTimeExpiredModal={
            modalsHook.showTimeExpiredModal || modalsHook.showAnswerCheckModal
          }
          isLastQuestion={gameHook.isLastQuestion}
          hasChecked={questionHook.hasChecked}
          onExit={gameHook.handleExitGame}
        />
      </div>
    );
  }

  // Render game summary if completed
  if (gameHook.gameCompleted && gameHook.submitResult) {
    return (
      <GameSummary2
        resultData={gameHook.submitResult?.data || gameHook.submitResult}
        questionsData={gameHook.questions}
        onRestart={gameHook.handleRestartGame}
        onExit={gameHook.handleExitGame}
      />
    );
  }

  // Main game render
  return (
    <DndContext
      sensors={dragDropHook.sensors}
      collisionDetection={dragDropHook.collisionDetection}
      onDragStart={dragDropHook.handleDragStart}
      onDragEnd={dragDropHook.handleDragEnd}
      modifiers={[]}
    >
      <div className="h-full">
        <div>{renderCurrentQuestion()}</div>

        {/* Drag overlay */}
        <DragOverlay style={{ zIndex: 9999 }}>
          {dragDropHook.activeOption ? (
            <DragOverlayContent option={dragDropHook.activeOption} />
          ) : null}
        </DragOverlay>

        {/* Modal System */}
        <AnswerCheckModal
          isOpen={modalsHook?.showAnswerCheckModal}
          currentQuestion={questionHook?.currentQuestion}
          userAnswers={questionHook?.blanks}
          isCorrect={questionHook?.isCorrect}
          onPlayAgain={modalsHook?.handleAnswerModalPlayAgain}
          onNextChallenge={modalsHook?.handleAnswerModalNextChallenge}
          currentQuestionIndex={gameHook?.currentQuestionIndex}
          totalQuestions={gameHook?.questions.length}
        />

        {!modalsHook?.showAnswerCheckModal && (
          <TimeExpiredModal
            isOpen={modalsHook?.showTimeExpiredModal}
            onSkip={modalsHook?.handleSkipQuestion}
            onPlayAgain={modalsHook?.handlePlayAgain}
            currentQuestion={gameHook?.currentQuestionIndex + 1}
            totalQuestions={gameHook?.questions.length}
          />
        )}
      </div>
    </DndContext>
  );
};

export default WaterfallGameMain;
