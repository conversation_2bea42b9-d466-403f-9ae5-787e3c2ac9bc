/**
 * Mobile device detection and touch utilities for decoration layer
 */

export const isMobileDevice = () => {
  if (typeof window === 'undefined') return false;
  
  return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(
    navigator.userAgent
  ) || window.innerWidth <= 768;
};

export const isTouchDevice = () => {
  if (typeof window === 'undefined') return false;
  
  return 'ontouchstart' in window || navigator.maxTouchPoints > 0;
};

export const getOptimalTouchTargetSize = () => {
  return isMobileDevice() ? 44 : 24; // 44px is Apple's recommended minimum touch target
};

export const getScaledCoordinates = (event, containerRef, scale = 1) => {
  if (!containerRef.current) return { x: 0, y: 0 };
  
  const rect = containerRef.current.getBoundingClientRect();
  const clientX = event.touches ? event.touches[0].clientX : event.clientX;
  const clientY = event.touches ? event.touches[0].clientY : event.clientY;
  
  return {
    x: (clientX - rect.left) / scale,
    y: (clientY - rect.top) / scale
  };
};

export const preventDefaultTouchBehavior = (element) => {
  if (!element) return;
  
  element.style.touchAction = 'none';
  element.style.webkitTouchCallout = 'none';
  element.style.webkitUserSelect = 'none';
  element.style.userSelect = 'none';
};

export const optimizeForMobile = (element) => {
  if (!element || !isMobileDevice()) return;
  
  preventDefaultTouchBehavior(element);
  
  // Add mobile-specific classes
  element.classList.add('mobile-optimized');
  
  // Optimize touch targets
  const touchTargets = element.querySelectorAll('button, [role="button"]');
  touchTargets.forEach(target => {
    const currentSize = Math.max(target.offsetWidth, target.offsetHeight);
    if (currentSize < 44) {
      target.style.minWidth = '44px';
      target.style.minHeight = '44px';
    }
  });
};