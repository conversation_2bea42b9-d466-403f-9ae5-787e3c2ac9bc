'use client';
import FormInput from '@/components/form/FormInput';
import api from '@/lib/api';
import { Icon } from '@iconify/react';
import { Field, Form, Formik } from 'formik';
import Link from 'next/link';
import React, { useState } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { useRouter } from 'next/navigation';
import { loginSuccess } from '@/store/features/authSlice';
import * as Yup from 'yup';

const validationSchema = Yup.object().shape({
  currentPassword: Yup.string().required('Please enter your current password'),
  newPassword: Yup.string()
    .required('Please enter a new password')
    .min(8, 'Password must be at least 8 characters')
    .matches(
      /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/,
      'Password must contain at least one uppercase letter, one lowercase letter, one number, and one special (@, $, !, %, *, ?, or &) character'
    ),
  confirmNewPassword: Yup.string()
    .oneOf([Yup.ref('newPassword'), null], 'Passwords must match')
    .required('Please confirm your new password'),
});

const ChangePassword = () => {
  const [showCurrentPassword, setShowCurrentPassword] = useState(false);
  const [showNewPassword, setShowNewPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const { user } = useSelector((state) => state.auth);
  const dispatch = useDispatch();
  const router = useRouter();

  const handleSubmit = async (values, { setSubmitting, setFieldError, setErrors, resetForm }) => {
    try {
      setSubmitting(true);

      const payload = {
        userId: user?.id,
        currentPassword: values.currentPassword,
        newPassword: values.newPassword,
        confirmNewPassword: values.confirmNewPassword,
        rememberMe: values.rememberMe || false
      };

      const response = await api.post('/auth/change-password', payload);

      if (response?.success) {
        // Update authentication state with new token and user data
        if (response?.data?.access_token && response?.data?.user) {
          // Update Redux state with new user data and token
          dispatch(loginSuccess({
            user: response.data.user,
            token: response.data.access_token,
            tokenExpires: response.data.token_expires
          }));

          // Navigate based on user role after successful password change
          const userRole = response.data.user.selectedRole;
          setTimeout(() => {
            if (userRole === 'student') {
              router.push('/diary');
            } else if (userRole === 'tutor' || userRole === 'admin') {
              router.push('/dashboard');
            } else {
              router.push('/');
            }
          }, 1500); // Small delay to show success message
        }

        resetForm();
      }
    } catch (error) {
      console.error('Change password error:', error);

      if (error?.response?.data?.validationErrors) {
        const validationErrors = error?.response?.data?.validationErrors;
        // Handle object format where keys are field names and values are arrays of error messages
        for (const field in validationErrors) {
          if (validationErrors.hasOwnProperty(field)) {
            const errorMessages = validationErrors[field];
            if (Array.isArray(errorMessages)) {
              setFieldError(field, errorMessages[0]);
            } else {
              setFieldError(field, errorMessages);
            }
          }
        }
      } else if (error?.response?.data?.message) {
        setErrors({ general: error.response.data.message });
      } else {
        setErrors({ general: 'Failed to change password. Please try again.' });
      }
    } finally {
      setSubmitting(false);
    }
  };
  return (
    <div className="flex flex-col items-center">
      <div className="w-full max-w-md bg-white rounded-lg">
        <div className="px-4 py-6 pb-0 space-y-4">
          <h1 className="text-xl font-bold text-gray-900 text-center">
            HELLO ENGLISH COACHING - HEC
          </h1>

          {/* <h2 className="text-lg font-semibold text-gray-800">LOGIN</h2> */}

          <p className="text-gray-600 text-center">Change your password</p>

          <Formik
            initialValues={{
              currentPassword: '',
              newPassword: '',
              confirmNewPassword: '',
              rememberMe: false,
            }}
            validationSchema={validationSchema}
            onSubmit={handleSubmit}
          >
            {({ isSubmitting, errors, values }) => (
              <Form className="space-y-5">
                {errors.general && (
                  <div className="text-red-500 text-sm text-center">
                    {errors.general}
                  </div>
                )}

                <div className="relative">
                  <FormInput
                    label="Current Password"
                    name="currentPassword"
                    type={showCurrentPassword ? 'text' : 'password'}
                    placeholder="Enter current password"
                    required
                  />
                  <button
                    type="button"
                    onClick={() => setShowCurrentPassword(!showCurrentPassword)}
                    className="absolute right-3 top-[42px] transform text-gray-500"
                  >
                    <Icon
                      icon={showCurrentPassword ? 'mdi:eye-off' : 'mdi:eye'}
                      width="16"
                      height="16"
                    />
                  </button>
                </div>
                <div className="relative">
                  <FormInput
                    label="New Password"
                    name="newPassword"
                    type={showNewPassword ? 'text' : 'password'}
                    placeholder="Enter new password"
                    required
                  />
                  <button
                    type="button"
                    onClick={() => setShowNewPassword(!showNewPassword)}
                    className="absolute right-3 top-[42px] transform text-gray-500"
                  >
                    <Icon
                      icon={showNewPassword ? 'mdi:eye-off' : 'mdi:eye'}
                      width="16"
                      height="16"
                    />
                  </button>
                </div>
                <div className="relative">
                  <FormInput
                    label="Confirm New Password"
                    name="confirmNewPassword"
                    type={showConfirmPassword ? 'text' : 'password'}
                    placeholder="Confirm new password"
                    required
                  />
                  <button
                    type="button"
                    onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                    className="absolute right-3 top-[42px] transform text-gray-500"
                  >
                    <Icon
                      icon={showConfirmPassword ? 'mdi:eye-off' : 'mdi:eye'}
                      width="16"
                      height="16"
                    />
                  </button>
                </div>

                <div className="flex items-center justify-between">
                  <label className="flex items-center cursor-pointer">
                    <Field
                      type="checkbox"
                      name="rememberMe"
                      className="h-4 w-4 text-blue-600 border-gray-300 rounded"
                    />
                    <span className="ml-2 text-sm text-gray-600">
                      Remember Me
                    </span>
                  </label>
                  <Link
                    href="/forgot-password"
                    className="text-sm text-red-500 hover:text-red-600"
                  >
                    Forgot Password?
                  </Link>
                </div>

                <button
                  type="submit"
                  disabled={
                    isSubmitting ||
                    !values.currentPassword ||
                    !values.newPassword ||
                    !values.confirmNewPassword
                  }
                  className={`w-full py-2 px-4 rounded-md transition-colors ${
                    isSubmitting ||
                    !values.currentPassword ||
                    !values.newPassword ||
                    !values.confirmNewPassword
                      ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                      : 'bg-yellow-300 text-gray-800 hover:bg-yellow-400'
                  }`}
                >
                  {isSubmitting ? 'Updating...' : 'Update Password'}
                </button>
              </Form>
            )}
          </Formik>
        </div>
      </div>
    </div>
  );
};

export default ChangePassword;
