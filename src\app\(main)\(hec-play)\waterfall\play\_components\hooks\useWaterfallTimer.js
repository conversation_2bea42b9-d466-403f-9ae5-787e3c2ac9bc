import { useState, useEffect, useRef } from 'react';

export const useWaterfallTimer = (initialTime, onTimeExpired, questionIndex = 0) => {
  const [timeLeft, setTimeLeft] = useState(initialTime || 0);
  const [gameStarted, setGameStarted] = useState(false);
  const [timerExpired, setTimerExpired] = useState(false);
  const onTimeExpiredRef = useRef(onTimeExpired);
  const hasExpiredRef = useRef(false);
  const currentTimeRef = useRef(initialTime || 0);



  // Update the ref when callback changes
  useEffect(() => {
    onTimeExpiredRef.current = onTimeExpired;
  }, [onTimeExpired]);

  // Update timer when initialTime changes (for different questions)
  useEffect(() => {
    const newTime = initialTime || 0;
    currentTimeRef.current = newTime;
    setTimeLeft(newTime);
    setTimerExpired(false);
    hasExpiredRef.current = false;

    // Auto-start timer if there's a valid time limit
    if (newTime > 0) {
      setGameStarted(true);
    } else {
      setGameStarted(false);
    }
  }, [initialTime, questionIndex]);

  // Timer countdown effect
  useEffect(() => {
    // console.log('Timer effect triggered:', {
    //   gameStarted,
    //   timerExpired,
    //   initialTime,
    //   timeLeft,
    //   shouldRun: gameStarted && !timerExpired && initialTime && initialTime > 0
    // });

    // Only run timer if game started, not expired, and there's a valid time limit
    if (!gameStarted || timerExpired || !initialTime || initialTime <= 0 || initialTime === null || initialTime === undefined) {
      console.log('Timer not starting due to conditions:', {
        gameStarted,
        timerExpired,
        initialTime,
        validTime: initialTime && initialTime > 0
      });
      return;
    }

    const timer = setInterval(() => {
      setTimeLeft((prevTime) => {
        // console.log('Timer tick:', prevTime);
        if (prevTime <= 1) {
          // console.log('Timer expired');
          setTimerExpired(true);
          // Use ref to prevent multiple calls and avoid dependency issues
          if (onTimeExpiredRef.current && !hasExpiredRef.current) {
            hasExpiredRef.current = true;
            setTimeout(() => {
              onTimeExpiredRef.current();
            }, 0);
          }
          return 0;
        }
        return prevTime - 1;
      });
    }, 1000);

    return () => {
      clearInterval(timer);
    };
  }, [gameStarted, timerExpired, initialTime]);

  const startTimer = () => {
    // Only start timer if there's a valid time limit
    // console.log('startTimer called:', { gameStarted, initialTime, hasValidTime: initialTime && initialTime > 0 });
    if (!gameStarted && initialTime && initialTime > 0) {
      // console.log('Starting timer with time:', initialTime);
      setGameStarted(true);
    }
  };

  const resetTimer = (newTime = null) => {
    const timeToSet = newTime !== null ? newTime : (initialTime || 0);
    // console.log('resetTimer called:', { newTime, timeToSet, currentTime: currentTimeRef.current });
    currentTimeRef.current = timeToSet;
    setTimeLeft(timeToSet);
    setTimerExpired(false);
    hasExpiredRef.current = false;

    // Auto-start timer if there's a valid time limit
    if (timeToSet > 0) {
      setGameStarted(true);
    } else {
      setGameStarted(false);
    }
  };

  const restartCurrentTimer = () => {
    setTimeLeft(currentTimeRef.current);
    setTimerExpired(false);
    hasExpiredRef.current = false;
    // Restart the game to ensure timer runs properly
    setGameStarted(true);
  };

  const formatTime = (seconds) => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
  };

  const formatTimeDigital = (seconds) => {
    const safeSeconds = Math.max(0, seconds || 0);
    const minutes = Math.floor(safeSeconds / 60);
    const remainingSeconds = safeSeconds % 60;
    return {
      minutes: minutes.toString().padStart(2, '0'),
      seconds: remainingSeconds.toString().padStart(2, '0')
    };
  };

  return {
    timeLeft,
    timerExpired,
    gameStarted,
    startTimer,
    resetTimer,
    restartCurrentTimer,
    formatTime,
    formatTimeDigital,
  };
};
