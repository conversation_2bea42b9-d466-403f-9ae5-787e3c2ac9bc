'use client';
import Button, { ButtonIcon } from '@/components/Button';
import <PERSON><PERSON>iewer from '@/components/EditorViewer';
import DetailsModal from '@/components/form/modal/MissionConfirmationModal';
import SimpleTiptapEditor from '@/components/form/SimpleTiptapEditor';
import GoBack from '@/components/shared/GoBack';
import useDataFetch from '@/hooks/useDataFetch';
import api from '@/lib/api';

import { Form, Formik } from 'formik';
import Image from 'next/image';
import { useParams, useRouter } from 'next/navigation';
import React, { useEffect, useRef, useState, useCallback } from 'react';
import { useSelector } from 'react-redux';
import CorrectionZone from '../../_components/CorrectionZone';

const CommonQnA = () => {
  const { id } = useParams();
  const getEndPoint = `/play/story-maker/play/${id}`;
  const submitEndPoint = `/play/story-maker/play/${id}/submit`;
  const router = useRouter();
  const [enableEdit, setEnableEdit] = useState(false);

  const editorRef = useRef(null);
  const [value, setValue] = useState('');
  const { user } = useSelector((state) => state.auth);
  const [isSubmitted, setIsSubmitted] = useState(false);
  const [showDetailsModal, setShowDetailsModal] = useState(false);

  const { data: questionDetails, refetch } = useDataFetch({
    queryKey: [`question-info`, getEndPoint],
    endPoint: getEndPoint,
  });

  const showSubmission =
    questionDetails?.latest_submission?.content &&
    !enableEdit &&
    questionDetails?.is_played;

  const handleSubmit = async (values, { setSubmitting }) => {
    try {
      setSubmitting(true);
      const response = await api.post(submitEndPoint, {
        content: values.content?.answer || values.content,
        story_maker_id: values?.story_maker_id,
      });
      console.log(response);
      setIsSubmitted(true);
      refetch();
      router.push('/story-maker');
    } catch (error) {
      console.log(error);
    } finally {
      setSubmitting(false);
    }
  };

  const handleUpdate = async () => {
    if (!value) return;

    try {
      const response = await api.put(
        `/play/story-maker/play/${id}`,
        {
          content: value,
        },
        { showSuccessToast: false }
      );

      console.log('Saved:', response);
      // setIsSubmitted(false);
    } catch (error) {
      console.error('Error updating:', error);
    }
  };

  useEffect(() => {
    const timeout = setTimeout(() => {
      handleUpdate();
    }, 1000);

    return () => clearTimeout(timeout);
  }, [value]);

  useEffect(() => {
    if (questionDetails?.latest_submission?.content) {
      setValue(questionDetails?.latest_submission?.content);
    }
  }, []);

  return (
    <div className="relative">
      <div className="max-w-7xl mx-auto px-5 xl:px-0 relative z-10">
        <GoBack title={'HEC Play'} linkClass="my-5 w-full max-w-40" />

        <div className="p-5 rounded-lg bg-[#FFF9FB] shadow-lg space-y-5 mb-10">
          <div className="p-5 bg-[#FCF8EF] rounded-lg [box-shadow:2px_2px_12px_0px_#F5D1B066_inset,_-2px_-2px_12px_0px_#F5D1B066_inset]">
            <h1 className="text-2xl text-yellow-800 font-semibold mb-4">
              {questionDetails?.title}
            </h1>
            <div className="flex max-sm:flex-col items-center gap-6">
              {questionDetails?.picture && (
                <Image
                  src={questionDetails?.picture}
                  alt={questionDetails?.title}
                  width={200}
                  height={200}
                />
              )}
              <div>
                {(questionDetails?.instructions ||
                  questionDetails?.description) && (
                  <p className="font-semibold text-lg text-gray-700">
                    Instruction:
                  </p>
                )}
                <EditorViewer
                  data={
                    questionDetails?.instruction || questionDetails?.description
                  }
                />
              </div>
            </div>
          </div>

          {showSubmission ? (
            <div className="space-y-3">
              <div className="p-5 shadow-lg rounded-lg border relative bg-white">
                <h1 className="text-xl text-yellow-800 font-semibold">
                  My Submission
                </h1>
                <EditorViewer
                  data={
                    questionDetails?.latest_submission?.content?.length > 200
                      ? questionDetails?.latest_submission?.content?.slice(
                          0,
                          400
                        ) + '...'
                      : questionDetails?.latest_submission?.content
                  }
                />

                <div className="absolute right-2 top-2">
                  <ButtonIcon
                    icon={'ri:edit-2-fill'}
                    innerBtnCls={'h-10 w-10'}
                    btnIconCls={'h-5 w-5'}
                    onClick={() => setEnableEdit(true)}
                  />
                </div>
              </div>

              <CorrectionZone questionDetails={questionDetails} />
            </div>
          ) : (
            <Formik
              initialValues={{
                content:
                  value || questionDetails?.latest_submission?.content || '',
              }}
              onSubmit={handleSubmit}
              enableReinitialize
            >
              {({ isSubmitting }) => (
                <Form>
                  <SimpleTiptapEditor
                    name="content"
                    editorRef={editorRef}
                    initialValue={
                      questionDetails?.latest_submission?.content || ''
                    }
                    onAutoSave={(content) => {
                      setValue(content);
                      handleUpdate({ answer: content, _autoSave: true });
                    }}
                    setValue={setValue}
                  />

                  <div className="flex justify-center mt-3 gap-3">
                    <Button
                      buttonText="Cancel"
                      type="button"
                      onClick={() => setIsSubmitted(false)}
                    />
                    <Button
                      disabled={isSubmitting}
                      buttonText={isSubmitting ? 'Submitting...' : 'Submit'}
                      type="submit"
                      className="bg-yellow-400 hover:bg-yellow-500 text-black"
                    />
                    {/* questionDetails?.latest_submission?.content
                          ? isSubmitting ? 'Updating...' : 'Update'
                          : isSubmitting ? 'Submitting...' : 'Submit' */}
                  </div>
                </Form>
              )}
            </Formik>
          )}
        </div>
      </div>

      <DetailsModal
        isOpen={showDetailsModal}
        onClose={() => setShowDetailsModal(false)}
        data={questionDetails?.submission?.corrections?.grammar}
        title="Teachers Feedback"
        link={`/answer`}
        showBtn={false}
      />
    </div>
  );
};

export default CommonQnA;
