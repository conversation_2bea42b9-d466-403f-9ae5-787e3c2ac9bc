import React from 'react';
import { motion } from 'framer-motion';
import { Icon } from '@iconify/react';
import Image from 'next/image';

const InitialInstructionsStage = ({ gameData, onStartGame }) => {
  if (!gameData) return null;

  const totalSentences = gameData?.sentences?.length || 0;
  const totalScore = gameData?.score || 0;

  const stageInstructions = [
    {
      stage: 1,
      title: 'Sentence Building Phase',
      description: `You'll build ${totalSentences} starting sentences using the words from Block Set #1`,
      icon: 'mdi:text-box-plus',
      color: 'text-blue-600',
      bgColor: 'bg-blue-50',
    },
    {
      stage: 2,
      title: 'Word Expansion Phase',
      description:
        'Expand each sentence using additional words from Block Set #2',
      icon: 'mdi:text-box-multiple',
      color: 'text-green-600',
      bgColor: 'bg-green-50',
    },
    {
      stage: 3,
      title: 'Results & Scoring',
      description: `See your completed sentences and earn up to ${totalScore} points!`,
      icon: 'mdi:trophy',
      color: 'text-yellow-600',
      bgColor: 'bg-yellow-50',
    },
  ];

  return (
    <div className="w-full max-w-5xl mx-auto px-4 sm:px-6">
      {/* Header Section */}
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6 }}
        className="text-center mb-6 sm:mb-8 flex flex-col sm:flex-row items-center justify-between relative p-4 sm:p-5 sm:py-8 bg-[#FCF8EF] rounded-lg [box-shadow:2px_2px_12px_0px_#F5D1B066_inset,_-2px_-2px_12px_0px_#F5D1B066_inset] gap-3"
      >
        <div className="relative">
          <Image
            src="/assets/images/all-img/catImg.png"
            alt="Game Character"
            width={80}
            height={80}
            className="mx-auto sm:mx-0 sm:w-[120px] sm:h-[120px]"
          />
        </div>

        <div className="flex-1">
          <h1 className="text-2xl sm:text-3xl lg:text-4xl font-bold text-[#8B4513]">
            Block Sentence Builder
          </h1>
          <p className="text-sm sm:text-base lg:text-lg text-[#5A3D1A] mb-2">
            Create amazing sentences using word blocks!
          </p>
          <div className="flex flex-col sm:flex-row items-center justify-center gap-2 sm:gap-4 text-xs sm:text-sm text-[#8B4513]">
            <div className="flex items-center gap-1">
              <Icon icon="mdi:format-list-numbered" className="w-3 h-3 sm:w-4 sm:h-4" />
              <span>{totalSentences} Sentences</span>
            </div>
            <div className="flex items-center gap-1">
              <Icon icon="mdi:star" className="w-3 h-3 sm:w-4 sm:h-4" />
              <span>Max {totalScore} Points</span>
            </div>
          </div>
        </div>

        {/* Start Game Button */}
        <motion.div
          // initial={{ opacity: 0, scale: 0.9 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.6, delay: 0.7 }}
          className="text-center"
        >
          <motion.button
            whileTap={{ scale: 0.95 }}
            onClick={onStartGame}
            className="bg-gradient-to-b from-yellow-500 to-yellow-400 hover:from-yellow-500 hover:to-yellow-500 text-white font-bold py-3 px-8 sm:py-4 sm:px-12 rounded-full text-lg sm:text-xl transition-all duration-300 flex items-center gap-2 sm:gap-3 mx-auto"
          >
            <Icon icon="mdi:play" className="w-5 h-5 sm:w-6 sm:h-6" />
            Start Game
            <motion.div
              animate={{ x: [0, 5, 0] }}
              transition={{ duration: 1.5, repeat: Infinity }}
            >
              <Icon icon="mdi:arrow-right" className="w-5 h-5 sm:w-6 sm:h-6" />
            </motion.div>
          </motion.button>
        </motion.div>

        {/* <motion.div
          animate={{ rotate: [0, 10, -10, 0] }}
          transition={{ duration: 2, repeat: Infinity, repeatDelay: 3 }}
          className="absolute -top-2 -right-2"
        >
          <Icon
            icon="mdi:star-four-points"
            className="w-8 h-8 text-yellow-400"
          />
        </motion.div> */}
      </motion.div>

      {/* Decorative Elements */}
      <motion.div
        // initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 1, delay: 1 }}
        className="my-8 text-center"
      >
        <Image
          src="/assets/Frame 1000007108.png"
          alt="Decoration"
          width={500}
          height={20}
          className="w-full h-auto opacity-60"
        />
      </motion.div>

      {/* Game Flow Instructions */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6, delay: 0.2 }}
        className="mb-8 max-w-4xl mx-auto"
      >
        <h2 className="text-2xl font-bold text-[#8B4513] text-center mb-8">
          How to Play
        </h2>

        <div className="space-y-6">
          {stageInstructions.map((stage, index) => (
            <motion.div
              key={stage.stage}
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.5, delay: 0.3 + index * 0.1 }}
              className={`${stage.bgColor} rounded-2xl p-6 border-2 border-opacity-20 border-gray-300`}
            >
              <div className="flex items-start gap-4">
                <div
                  className={`${stage.color} ${stage.bgColor} p-3 rounded-full border-2 border-current border-opacity-20`}
                >
                  <Icon icon={stage.icon} className="w-6 h-6" />
                </div>
                <div className="flex-1">
                  <div className="flex max-sm:flex-col sm:items-center gap-2 mb-2">
                    <span className="bg-white max-sm:max-w-20 px-3 py-1 rounded-full text-sm font-bold text-[#8B4513]">
                      Stage {stage.stage}
                    </span>
                    <h3 className="text-lg font-bold text-[#8B4513]">
                      {stage.title}
                    </h3>
                  </div>
                  <p className="text-[#5A3D1A]">{stage.description}</p>
                </div>
              </div>
            </motion.div>
          ))}

          {/* Game Tips */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.5 }}
            className="bg-gradient-to-r from-orange-50 to-yellow-50 rounded-2xl p-6 mb-8 border-2 border-orange-200"
          >
            <div className="flex items-start gap-3">
              <Icon
                icon="mdi:lightbulb"
                className="w-6 h-6 text-orange-500 mt-1"
              />
              <div>
                <h3 className="font-bold text-[#8B4513] mb-2">Pro Tips:</h3>
                <ul className="text-[#5A3D1A] space-y-1 text-sm">
                  <li>
                    • Drag and drop words from the blocks to build your
                    sentences
                  </li>
                  <li>
                    • You can remove words by clicking on them in your sentence
                  </li>
                  <li>
                    • Complete all starting sentences before moving to expansion
                  </li>
                  <li>• Take your time - there's no time limit!</li>
                </ul>
              </div>
            </div>
          </motion.div>
        </div>
      </motion.div>
    </div>
  );
};

export default InitialInstructionsStage;
