import { useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useRouter } from 'next/navigation';
import {
  setInitialData,
  setCurrentQuestionIndex,
  resetAllState,
  setShouldFetchNewData,
  selectGameCompleted,
  selectSubmitResult,
  selectShouldFetchNewData,
  selectCurrentQuestionIndex,
  selectQuestions,
  selectSetId,
} from '@/store/features/waterfallSlice';

export const useWaterfallGame = (initialData, refetchGame) => {
  const dispatch = useDispatch();
  const router = useRouter();
  
  const gameCompleted = useSelector(selectGameCompleted);
  const submitResult = useSelector(selectSubmitResult);
  const shouldFetchNewData = useSelector(selectShouldFetchNewData);
  const currentQuestionIndex = useSelector(selectCurrentQuestionIndex);
  const questions = useSelector(selectQuestions);
  const setId = useSelector(selectSetId);
  const score = useSelector(state => state.waterfall.score);

  // Set initial data when it changes and clear any existing localStorage
  useEffect(() => {
    if (initialData) {
      dispatch(setInitialData(initialData));

      // Clear any existing localStorage data for waterfall games
      if (typeof window !== 'undefined') {
        const keys = Object.keys(localStorage);
        keys.forEach(key => {
          if (key.startsWith('waterfall_game_')) {
            localStorage.removeItem(key);
          }
        });
      }
    }
  }, [initialData, dispatch]);

  // Reset all states when initialData changes (data refetch) - but only for intentional actions
  useEffect(() => {
    if (initialData) {
      // Reset all game states to initial values only when it's an intentional action
      dispatch(resetAllState());
      dispatch(setInitialData(initialData));

      // Reset the flag after processing
      dispatch(setShouldFetchNewData(false));
    }
  }, [initialData?.id, dispatch]);

  // Game control functions
  const handleRestartGame = () => {
    // Set flag to indicate this is an intentional action
    dispatch(setShouldFetchNewData(true));

    // If refetchGame function is provided, call it to get new questions
    if (typeof refetchGame === 'function') {
      refetchGame();
    } else {
      // If no refetch function, reset states immediately
      dispatch(resetAllState());
      dispatch(setInitialData(initialData));
    }
  };

  const handleExitGame = () => {
    // Reset all game state
    dispatch(resetAllState());

    // Navigate to waterfall route
    router.push('/waterfall');
  };

  const proceedToNextQuestion = () => {
    if (currentQuestionIndex < questions.length - 1) {
      dispatch(setCurrentQuestionIndex(currentQuestionIndex + 1));
    } else {
      // Handle game completion - this will be implemented in useWaterfallSubmission
      return 'GAME_COMPLETE';
    }
  };

  return {
    // State
    gameCompleted,
    submitResult,
    questions,
    setId,
    currentQuestionIndex,
    score,

    // Actions
    handleRestartGame,
    handleExitGame,
    proceedToNextQuestion,

    // Utils
    isLastQuestion: currentQuestionIndex === questions.length - 1,
  };
};
