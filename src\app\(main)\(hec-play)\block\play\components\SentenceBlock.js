import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Icon } from '@iconify/react';
import { useRouter } from 'next/navigation';
import WordBlock from './WordBlock';
import DropZone from './DropZone';

const SentenceBlock = ({
  show = true,
  hide = false,
  type = 'start', // 'start' or 'expand'
  currentStep,
  gameData,
  startingSentences,
  expandingSentences,
  usedWords,
  handleDragStart,
  handleDrop,
  removeWord,
  onNext,
  onSubmit,
  allComplete = false,
  title,
  showCompletedSentences = false,
  allSentencesBuilt = false,
  onStartExpansion,
  onSentenceComplete,
  onExit,
}) => {
  const router = useRouter();
  const [selectedWord, setSelectedWord] = useState(null);
  const [isMobile, setIsMobile] = useState(false);

  // Detect mobile device
  useEffect(() => {
    const checkMobile = () => {
      if (window.innerWidth <= 768 || 'ontouchstart' in window) {
        setIsMobile(true);
      } else {
        setIsMobile(false);
      }
    };

    checkMobile();
    window.addEventListener('resize', checkMobile);
    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  // Handle mobile word selection
  const handleWordClick = (word) => {
    if (!isMobile) return;

    if (selectedWord === word) {
      setSelectedWord(null);
    } else {
      setSelectedWord(word);
      if (handleDragStart) {
        handleDragStart(word, dragType);
      }
    }
  };

  // Handle mobile drop zone click
  const handleDropZoneClick = (dropIndex, dropBlockIndex) => {
    if (!isMobile || !selectedWord) return;

    if (handleDrop) {
      handleDrop(dragType, dropIndex, dropBlockIndex);
      setSelectedWord(null);
    }
  };
  if (hide || !show || !gameData) return null;

  const isStartingType = type === 'start';
  const words = isStartingType
    ? gameData.word_blocks.starting_words
    : gameData.word_blocks.expanding_words;

  const sentences = isStartingType ? startingSentences : expandingSentences;
  const dragType = isStartingType ? 'start' : 'expand';

  const getTitle = () => {
    if (title) return title;
    return isStartingType
      ? `Starting Sentence Block set #1`
      : 'Expanding Sentence Block Set #2';
  };

  const getSubtitle = () => {
    if (title) return title;
    return isStartingType
      ? `Please drag words to build the starting sentences.`
      : 'Please drag words to accomplish the expanding sentences';
  };

  // const renderProgressIndicator = () => {
  //   const totalSentences = gameData.sentences.length;

  //   if (isStartingType) {
  //     const completedSentences = startingSentences.filter(s => s.every(w => w !== null)).length;
  //     const progressPercentage = totalSentences > 0 ? (completedSentences / totalSentences) * 100 : 0;

  //     return (
  //       <motion.div
  //         initial={{ opacity: 0, y: -10 }}
  //         animate={{ opacity: 1, y: 0 }}
  //         transition={{ duration: 0.5 }}
  //         className="mb-6"
  //       >
  //         <div className="bg-white rounded-2xl p-4 shadow-md border-2 border-orange-200">
  //           <div className="flex items-center justify-between mb-3">
  //             <h3 className="text-lg font-bold text-[#8B4513] flex items-center gap-2">
  //               <Icon icon="mdi:progress-check" className="w-5 h-5" />
  //               Sentence Building Progress
  //             </h3>
  //             <span className="text-sm font-medium text-[#5A3D1A]">
  //               {completedSentences}/{totalSentences} Complete
  //             </span>
  //           </div>

  //           <div className="w-full bg-gray-200 rounded-full h-3 mb-2">
  //             <motion.div
  //               className="bg-gradient-to-r from-orange-400 to-orange-500 h-full rounded-full transition-all duration-500"
  //               initial={{ width: 0 }}
  //               animate={{ width: `${progressPercentage}%` }}
  //               transition={{ duration: 0.8, delay: 0.2 }}
  //             />
  //           </div>

  //           <div className="flex justify-between text-xs text-[#5A3D1A]">
  //             <span>Current: Sentence {currentStep + 1}</span>
  //             <span>{Math.round(progressPercentage)}% Complete</span>
  //           </div>
  //         </div>
  //       </motion.div>
  //     );
  //   } else {
  //     // Word expansion progress
  //     const completedExpansions = expandingSentences.filter(s => s.every(w => w !== null)).length;
  //     const progressPercentage = (completedExpansions / totalSentences) * 100;

  //     return (
  //       <motion.div
  //         initial={{ opacity: 0, y: -10 }}
  //         animate={{ opacity: 1, y: 0 }}
  //         transition={{ duration: 0.5 }}
  //         className="mb-6"
  //       >
  //         <div className="bg-white rounded-2xl p-4 shadow-md border-2 border-green-200">
  //           <div className="flex items-center justify-between mb-3">
  //             <h3 className="text-lg font-bold text-[#8B4513] flex items-center gap-2">
  //               <Icon icon="mdi:text-box-plus" className="w-5 h-5" />
  //               Word Expansion Progress
  //             </h3>
  //             <span className="text-sm font-medium text-[#5A3D1A]">
  //               {completedExpansions}/{totalSentences} Expanded
  //             </span>
  //           </div>

  //           <div className="w-full bg-gray-200 rounded-full h-3 mb-2">
  //             <motion.div
  //               className="bg-gradient-to-r from-green-400 to-green-500 h-full rounded-full transition-all duration-500"
  //               initial={{ width: 0 }}
  //               animate={{ width: `${progressPercentage}%` }}
  //               transition={{ duration: 0.8, delay: 0.2 }}
  //             />
  //           </div>

  //           <div className="flex justify-between text-xs text-[#5A3D1A]">
  //             <span>Expanding all sentences</span>
  //             <span>{Math.round(progressPercentage)}% Complete</span>
  //           </div>
  //         </div>
  //       </motion.div>
  //     );
  //   }
  // };

  const renderWordBlocks = () => {
    // Check if all words are used
    const allWordsUsed = words.every((word) => usedWords.includes(word));

    return (
      <AnimatePresence>
        {!allWordsUsed && (
          <motion.div
            initial={{ opacity: 0, y: -20, scale: 0.95 }}
            animate={{ opacity: 1, y: 0, scale: 1 }}
            exit={{ opacity: 0, y: -20, scale: 0.95 }}
            transition={{ duration: 0.5, ease: 'easeInOut' }}
          >
            <WordBlock
              words={words}
              usedWords={usedWords}
              title={getTitle()}
              subtitle={getSubtitle()}
              hide={false}
              disable={false}
              disableDrag={false}
              disableClick={!isMobile}
              dragType={dragType}
              onDragStart={handleDragStart}
              onClick={handleWordClick}
              selectedWord={selectedWord}
              showImage={true}
              imageSrc="/assets/images/all-img/footer_butterfly.png"
              imageWidth={50}
              imageHeight={50}
              imageAlt="Icon"
            />
          </motion.div>
        )}
      </AnimatePresence>
    );
  };

  const renderDropZones = () => {
    if (isStartingType) {
      return (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, ease: 'easeInOut' }}
          className="bg-white rounded-2xl sm:rounded-[32px] p-3 py-2 flex flex-wrap gap-2 sm:gap-3"
        >
          {sentences[currentStep].map((word, i) => (
            <motion.div
              key={i}
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.5, delay: i * 0.1, ease: 'easeInOut' }}
            >
              <DropZone
                word={word}
                index={i}
                blockIndex={currentStep}
                hide={false}
                disable={false}
                disableDrop={false}
                disableRemove={false}
                dragType={dragType}
                onDrop={handleDrop}
                onRemoveWord={removeWord}
                onClick={() => handleDropZoneClick(i, currentStep)}
                isMobile={isMobile}
                selectedWord={selectedWord}
              />
            </motion.div>
          ))}
        </motion.div>
      );
    } else {
      // Expanding sentences - show all sentences
      return gameData.sentences.map((_, blockIndex) => (
        <motion.div
          key={blockIndex}
          initial={{ opacity: 0, x: -20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{
            duration: 0.5,
            delay: blockIndex * 0.1,
            ease: 'easeInOut',
          }}
          className="w-full flex flex-col sm:flex-row gap-2 sm:gap-4 items-start sm:items-center bg-white rounded-xl p-3 sm:p-4 max-h-40 overflow-y-auto border border-gray-200"
        >
          <motion.p
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.5, delay: 0.2 }}
            className="text-sm sm:text-base font-medium text-gray-700 min-w-0 flex-shrink"
          >
            <span className="font-bold text-orange-600">{blockIndex + 1}.</span>{' '}
            {startingSentences[blockIndex].join(' ')}
          </motion.p>
          <div className="flex flex-wrap gap-2 sm:gap-3 flex-1">
            {sentences[blockIndex].map((word, i) => (
              <motion.div
                key={i}
                initial={{ opacity: 0, scale: 0.8 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{
                  duration: 0.5,
                  delay: blockIndex * 0.1 + i * 0.05,
                  ease: 'easeInOut',
                }}
              >
                <DropZone
                  word={word}
                  index={i}
                  blockIndex={blockIndex}
                  hide={false}
                  disable={false}
                  disableDrop={false}
                  disableRemove={false}
                  dragType={dragType}
                  onDrop={handleDrop}
                  onRemoveWord={removeWord}
                  onClick={() => handleDropZoneClick(i, blockIndex)}
                  isMobile={isMobile}
                  selectedWord={selectedWord}
                />
              </motion.div>
            ))}
          </div>
        </motion.div>
      ));
    }
  };

  const renderCompletedStartingSentences = () => {
    if (!isStartingType) return null;

    const completedSentences = [];
    // Include all completed sentences up to and including the current step
    for (let i = 0; i <= currentStep; i++) {
      if (
        startingSentences[i] &&
        startingSentences[i].every((w) => w !== null)
      ) {
        completedSentences.push({
          index: i,
          sentence: startingSentences[i].join(' '),
        });
      }
    }

    return (
      <AnimatePresence>
        {completedSentences.length > 0 && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            transition={{ duration: 0.5, ease: 'easeInOut' }}
            className="w-full space-y-2"
          >
            <motion.h3
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.5, delay: 0.1 }}
              className="text-base sm:text-lg font-bold text-green-700"
            >
              Completed Starting Sentences
            </motion.h3>
            <AnimatePresence>
              <div className="max-sm:max-h-24 sm:max-h-40 overflow-y-auto space-y-2">
                {completedSentences.map(({ index, sentence }, i) => (
                  <motion.div
                    key={index}
                    initial={{ opacity: 0, scale: 0.9, y: 20 }}
                    animate={{ opacity: 1, scale: 1, y: 0 }}
                    exit={{ opacity: 0, scale: 0.9, y: -20 }}
                    transition={{
                      duration: 0.5,
                      delay: i * 0.1,
                      ease: 'easeInOut',
                    }}
                    className="bg-white rounded-xl p-3 py-2 sm:p-4"
                  >
                    <motion.p
                      initial={{ opacity: 0 }}
                      animate={{ opacity: 1 }}
                      transition={{ duration: 0.5, delay: 0.2 }}
                      className="text-xs sm:text-sm font-medium text-green-700"
                    >
                      <span className="font-bold">{index + 1}.</span> {sentence}
                    </motion.p>
                  </motion.div>
                ))}
              </div>
            </AnimatePresence>
          </motion.div>
        )}
      </AnimatePresence>
    );
  };

  const renderCompletedSentences = () => {
    // Only show completed sentences when in expanding mode and all sentences are complete
    if (isStartingType || !allComplete) return null;

    return (
      <motion.div
        initial={{ opacity: 0, y: 30, scale: 0.95 }}
        animate={{ opacity: 1, y: 0, scale: 1 }}
        transition={{ duration: 0.5, ease: 'easeInOut' }}
        className="w-full bg-white border-2 border-green-300 rounded-xl sm:rounded-[32px] overflow-hidden px-4 sm:px-6 py-3 sm:py-4"
      >
        <motion.h3
          initial={{ opacity: 0, y: -10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.1 }}
          className="text-base sm:text-lg font-bold mb-3 sm:mb-4 text-green-700"
        >
          All Completed Sentences
        </motion.h3>
        <div className="space-y-3 sm:space-y-4 max-h-40 overflow-y-auto">
          {startingSentences.map((s, i) => {
            const start = s.join(' ');
            const expand = expandingSentences[i].join(' ');
            return (
              <motion.div
                key={i}
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{
                  duration: 0.5,
                  delay: i * 0.1,
                  ease: 'easeInOut',
                }}
                className="flex flex-wrap gap-1 sm:gap-2 items-center p-2 sm:p-3 bg-gray-50 rounded-lg"
              >
                <motion.span
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  transition={{ duration: 0.5, delay: 0.2 }}
                  className="font-bold text-orange-600 text-xs sm:text-sm"
                >
                  {i + 1}.
                </motion.span>
                <motion.span
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  transition={{ duration: 0.5, delay: 0.3 }}
                  className="text-xs sm:text-sm text-[#5A3D1A] font-medium"
                >
                  {start}
                </motion.span>
                {expand.split(' ').map((w, idx) => (
                  <motion.span
                    key={idx}
                    initial={{ opacity: 0, scale: 0.8 }}
                    animate={{ opacity: 1, scale: 1 }}
                    transition={{
                      duration: 0.5,
                      delay: 0.4 + idx * 0.05,
                      ease: 'easeInOut',
                    }}
                    className="text-xs sm:text-sm text-[#EA7D00] font-medium bg-orange-100 px-1 sm:px-2 py-0.5 sm:py-1 rounded"
                  >
                    {w}
                  </motion.span>
                ))}
              </motion.div>
            );
          })}
        </div>
      </motion.div>
    );
  };

  const renderActionButton = () => {
    const currentSentenceComplete = sentences[currentStep]?.every(
      (w) => w !== null
    );
    const isLastSentence = currentStep + 1 >= gameData.sentences.length;

    let buttonText = '';
    let onClickAction = () => {};
    let isDisabled = false;

    if (isStartingType) {
      if (currentSentenceComplete) {
        if (isLastSentence) {
          buttonText = 'Start Expanding Words';
          onClickAction = onNext;
        } else {
          buttonText = 'Next';
          onClickAction = () => {
            if (onSentenceComplete) {
              onSentenceComplete(currentStep + 1);
            }
            onNext();
          };
        }
      } else {
        // Incomplete sentence → disable button
        buttonText = isLastSentence
          ? isMobile
            ? 'Next'
            : 'Start Expanding Words'
          : 'Next';
        isDisabled = true;
      }
    } else if (!isStartingType && allComplete && onSubmit) {
      buttonText = 'Complete Game';
      onClickAction = onSubmit;
    } else {
      // Incomplete game → disable button
      buttonText = 'Complete Game';
      isDisabled = true;
    }

    const handleExit = () => {
      if (onExit) {
        onExit();
      } else {
        router.push('/block');
      }
    };

    return (
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="text-center space-y-3"
      >
        <div className="flex gap-3 justify-center items-center">
          <motion.button
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            onClick={handleExit}
            className="bg-gray-200 text-gray-500 rounded-full font-semibold shadow-lg transition-all duration-300 flex items-center gap-2 px-5 max-sm:px-4 py-2 hover:from-red-500 hover:to-red-600"
          >
            <Icon icon="mdi:exit-to-app" className="w-4 h-4" />
            <span>Exit</span>
          </motion.button>
          <motion.button
            whileHover={!isDisabled ? { scale: 1.05 } : {}}
            whileTap={!isDisabled ? { scale: 0.95 } : {}}
            onClick={!isDisabled ? onClickAction : () => {}}
            disabled={isDisabled}
            className={`bg-gradient-to-r from-yellow-500 to-yellow-600 text-white rounded-full font-bold shadow-lg transition-all duration-300 flex items-center gap-2 px-5 py-2 ${
              isDisabled
                ? 'opacity-50 cursor-not-allowed'
                : 'hover:from-yellow-600 hover:to-yellow-700'
            }`}
          >
            <span>{buttonText}</span>
            <Icon
              icon={
                buttonText === 'Complete Game'
                  ? ''
                  : buttonText === 'Start Expanding Words'
                  ? ''
                  : 'mage:arrow-right'
              }
              className="w-4 h-4"
            />

            {buttonText === 'Start Expanding Words' && (
              <motion.div
                animate={{ x: [0, 5, 0] }}
                transition={{ duration: 1.5, repeat: Infinity }}
              >
                <Icon icon="mage:arrow-right" className="w-5 h-5" />
              </motion.div>
            )}

            {buttonText === 'Complete Game' && (
              <motion.div
                animate={{ scale: [1, 1.2, 1] }}
                transition={{ duration: 1.5, repeat: Infinity }}
              >
                <Icon icon="mdi:trophy" className="w-5 h-5" />
              </motion.div>
            )}
          </motion.button>
        </div>
      </motion.div>
    );
  };

  return (
    <div className="relative w-full space-y-4 sm:space-y-6">
      {/* Progress indicator - only show outside modal */}
      {/* <div className="block lg:hidden">
        {renderProgressIndicator()}
      </div> */}

      {/* Main content area */}
      <div className="space-y-4 sm:space-y-6 pb-20">
        {renderWordBlocks()}
        <div className='max-h-60 overflow-y-auto space-y-2'>{renderDropZones()}</div>
        {renderCompletedStartingSentences()}
        {renderCompletedSentences()}
      </div>

      {/* Fixed bottom-right action button - positioned relative to modal */}
      <div className="absolute bottom-0 right-6 z-10">
        {renderActionButton()}
      </div>
    </div>
  );
};

export default SentenceBlock;
