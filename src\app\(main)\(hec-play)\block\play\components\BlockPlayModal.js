import React, { useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Icon } from '@iconify/react';

const BlockPlayModal = ({
  isOpen,
  onClose,
  children,
  currentStep = 0,
  totalSteps = 1,
  stageTitle = 'Game Progress',
  showProgress = true,
  completedSteps = 0,
}) => {
  const progressPercentage =
    totalSteps > 0 ? (completedSteps / totalSteps) * 100 : 0;

  // Prevent background scrolling when modal is open
  useEffect(() => {
    if (isOpen) {
      // Save current scroll position
      const scrollY = window.scrollY;

      // Prevent scrolling
      document.body.style.position = 'fixed';
      document.body.style.top = `-${scrollY}px`;
      document.body.style.width = '100%';
      document.body.style.overflow = 'hidden';

      return () => {
        // Restore scrolling
        document.body.style.position = '';
        document.body.style.top = '';
        document.body.style.width = '';
        document.body.style.overflow = '';

        // Restore scroll position
        window.scrollTo(0, scrollY);
      };
    }
  }, [isOpen]);

  return (
    <AnimatePresence>
      {isOpen && (
        <motion.div
          //   animate={{ opacity: 1 }}
          //   exit={{ opacity: 0 }}
          className="fixed -top-10 left-0 right-0 bottom-0 bg-black bg-opacity-25 flex items-center justify-center z-50 p-4"
          onClick={(e) => e.stopPropagation()} // Prevent clicks from bubbling
          // onWheel={(e) => e.preventDefault()} // Prevent scrolling
          // onTouchMove={(e) => e.preventDefault()} // Prevent touch scrolling
        >
          <motion.div
            initial={{ scale: 0.9, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            exit={{ scale: 0.9, opacity: 0 }}
            className="bg-white rounded-2xl shadow-2xl w-full max-w-6xl overflow-hidden flex flex-col relative bg-yellow-200"
            onClick={(e) => e.stopPropagation()} // Prevent clicks from bubbling
          >
            {/* Overlay gradient for better content readability */}
            <div className="absolute inset-0 bg-gradient-to-br from-white/90 via-white/85 to-white/90 pointer-events-none" />

            {/* Content wrapper with relative positioning */}
            <div className="relative z-10 flex flex-col h-full max-h-[95vh] overflow-y-auto">
            {/* Header with Progress and Close Button */}
            <div
              className={`${
                stageTitle.includes('Expanding')
                  ? 'bg-gradient-to-r from-yellow-500 to-yellow-400'
                  : 'bg-gradient-to-r from-yellow-500 to-yellow-400'
              } text-white transition-all duration-500 relative`}
            >
              <div className="p-4 flex items-center justify-between">
                <div className="flex items-center gap-4 flex-1">
                  <div className="flex items-center gap-2">
                    <motion.div
                      key={stageTitle}
                      initial={{ scale: 0 }}
                      animate={{ scale: 1 }}
                      transition={{ duration: 0.3 }}
                    >
                      <Icon
                        icon={
                          stageTitle.includes('Expanding')
                            ? 'mdi:text-box-plus'
                            : 'mdi:text-box'
                        }
                        className="w-6 h-6"
                      />
                    </motion.div>
                    <h2 className="text-lg font-bold">{stageTitle}</h2>
                  </div>
                  {showProgress && (
                    <div className="text-sm">
                      <span>
                        {completedSteps}/{totalSteps}
                      </span>
                    </div>
                  )}
                </div>

                <button
                  onClick={onClose}
                  className="p-2 hover:bg-white hover:bg-opacity-20 rounded-full transition-colors"
                >
                  <Icon icon="mdi:close" className="w-6 h-6" />
                </button>
              </div>

              {/* Progress bar at bottom of header - like scroll progress bar */}
              {showProgress && (
                <div className="absolute bottom-0 left-0 right-0 h-1 bg-white bg-opacity-30">
                  <motion.div
                    className="bg-yellow-600 rounded-r-full h-full transition-all duration-500"
                    initial={{ width: 0 }}
                    animate={{ width: `${progressPercentage}%` }}
                  />
                </div>
              )}
            </div>

            {/* Content Area */}
            <div className="flex-1 overflow-y-auto p-4 sm:p-6 relative">
              <AnimatePresence mode="wait">
                <motion.div
                  key={stageTitle}
                  initial={{ opacity: 0, x: 20 }}
                  animate={{ opacity: 1, x: 0 }}
                  exit={{ opacity: 0, x: -20 }}
                  transition={{ duration: 0.3 }}
                >
                  {children}
                </motion.div>
              </AnimatePresence>
            </div>
            </div>
          </motion.div>
        </motion.div>
      )}
    </AnimatePresence>
  );
};

export default BlockPlayModal;
