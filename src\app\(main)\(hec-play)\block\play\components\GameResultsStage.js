import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { Icon } from '@iconify/react';
import Image from 'next/image';
import Link from 'next/link';

const GameResultsStage = ({
  gameResults,
  gameData,
  startingSentences,
  expandingSentences,
  onRestart,
}) => {
  if (!gameResults || !gameData) return null;

  const totalSentences = gameData?.sentences?.length || 0;
  const score = gameResults?.score || 0;
  const maxScore = gameData?.score || 0;
  const percentage = maxScore > 0 ? Math.round((score / maxScore) * 100) : 0;
  const sentencesResult = gameResults?.sentence_results || [];
  const [viewResult, setVeiwResult] = useState(null);

  // Performance level based on percentage
  const getPerformanceLevel = () => {
    if (percentage >= 90)
      return {
        level: 'Excellent!',
        color: 'text-green-600',
        icon: 'mdi:trophy',
      };
    if (percentage >= 75)
      return { level: 'Great Job!', color: 'text-blue-600', icon: 'mdi:medal' };
    if (percentage >= 60)
      return {
        level: 'Good Work!',
        color: 'text-yellow-600',
        icon: 'mdi:star',
      };
    return {
      level: 'Keep Trying!',
      color: 'text-orange-600',
      icon: 'hugeicons:workout-stretching',
    };
  };

  const performance = getPerformanceLevel();

  return (
    <div className="w-full max-w-5xl mx-auto px-4 sm:px-6">
      {/* Header with Results */}
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="bg-yellow-100 border rounded-xl shadow p-4 sm:p-6 lg:p-8 mb-6 sm:mb-8"
      >
        <div className="flex flex-col sm:flex-row items-center justify-between gap-4 sm:gap-6">
          {/* Character and Title */}
          <div className="flex flex-col sm:flex-row items-center text-center sm:text-left">
            <div className="w-20 h-20 sm:w-28 sm:h-28 bg-gray-50 rounded-2xl flex items-center justify-center mb-4 sm:mb-0 sm:mr-6">
              <Image
                src="/assets/images/all-img/catImg.png"
                alt="Cat"
                width={60}
                height={60}
                className="object-contain sm:w-[80px] sm:h-[80px]"
              />
            </div>
            <div>
              <h1 className="text-xl sm:text-2xl lg:text-3xl font-bold text-gray-800 mb-2">
                Game Completed!
              </h1>
              <p
                className={`text-lg sm:text-xl font-semibold ${performance.color} flex items-center justify-center sm:justify-start gap-2`}
              >
                <Icon
                  icon={performance.icon}
                  className="w-5 h-5 sm:w-6 sm:h-6"
                />
                {performance.level}
              </p>
            </div>
          </div>

          {/* Score Display */}
          <div className="relative flex-shrink-0">
            <Image
              src="/assets/images/all-img/summaryScore.png"
              alt="Score Badge"
              width={250}
              height={125}
              className="sm:w-[300px] sm:h-[150px]"
            />
            <div className="absolute inset-0 flex flex-col items-center justify-center">
              <div className="text-lg sm:text-2xl font-bold text-yellow-700">
                Your Score
              </div>
              <div className="text-xl sm:text-2xl font-bold text-yellow-800">
                {score}/{maxScore}
              </div>
            </div>
          </div>
        </div>
      </motion.div>

      {/* Performance Stats */}
      {/* <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.2 }}
        className="grid grid-cols-1 sm:grid-cols-3 gap-3 sm:gap-4 mb-6 sm:mb-8"
      >
        <div className="bg-blue-50 rounded-xl p-3 sm:p-4 text-center border-2 border-blue-200">
          <Icon icon="pajamas:work-item-requirement" className="w-6 h-6 text-blue-600 mx-auto mb-2" />
          <div className="text-xl sm:text-2xl font-bold text-blue-800">{totalSentences}</div>
          <div className="text-xs sm:text-sm text-blue-600">Sentences Built</div>
        </div>
        <div className="bg-green-50 rounded-xl p-3 sm:p-4 text-center border-2 border-green-200">
          <Icon icon="ic:round-done-all" className="w-6 h-6 sm:w-7 sm:h-7 text-green-600 mx-auto mb-2" />
          <div className="text-xl sm:text-2xl font-bold text-green-800">{totalSentences}</div>
          <div className="text-xs sm:text-sm text-green-600">Completed</div>
        </div>
        <div className="bg-yellow-50 rounded-xl p-3 sm:p-4 text-center border-2 border-yellow-200">
          <Icon icon="solar:star-bold" className="w-6 h-6 sm:w-7 sm:h-7 text-yellow-600 mx-auto mb-2" />
          <div className="text-xl sm:text-2xl font-bold text-yellow-800">{percentage}%</div>
          <div className="text-xs sm:text-sm text-yellow-600">Accuracy</div>
        </div>
      </motion.div> */}

      {/* Completed Sentences Display */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.4 }}
        className="bg-white border rounded-xl shadow p-6 mb-8"
      >
        <div className="border-2 border-yellow-600 bg-yellow-100 rounded-xl p-2 mb-6">
          <h2 className="text-lg font-semibold text-center text-yellow-700 flex items-center justify-center gap-2">
            <Icon icon="mdi:text-box-multiple" className="w-6 h-6" />
            Your Completed Sentences
          </h2>
        </div>

        <div className="space-y-2 max-h-64 overflow-y-auto">
          {sentencesResult?.map((item, index) => {
            const startTextWords = item?.student_starting?.split(' ') || [];
            const expandTextWords = item?.student_expanding?.split(' ') || [];

            const correctStarting = item?.correct_starting?.split(' ') || [];
            const correctExpanding = item?.correct_expanding?.split(' ') || [];

            // Create all possible correct complete sentences from all sentence results
            const allCorrectSentences =
              sentencesResult?.map((sentenceItem) => {
                const correctStart = sentenceItem?.correct_starting || '';
                const correctExpand = sentenceItem?.correct_expanding || '';
                return `${correctStart} ${correctExpand}`.trim();
              }) || [];

            // Create student's complete sentence
            const studentCompleteSentence = `${item?.student_starting || ''} ${
              item?.student_expanding || ''
            }`.trim();

            // Find the matching correct sentence (if any)
            const matchingCorrectSentence = allCorrectSentences.find(
              (correctSentence) =>
                correctSentence.toLowerCase() ===
                studentCompleteSentence.toLowerCase()
            );

            // Check if the complete sentence is correct
            const isCompleteSentenceCorrect = !!matchingCorrectSentence;

            // Get words from the matching correct sentence or current item's correct sentence as fallback
            const correctSentenceToUse =
              matchingCorrectSentence ||
              `${item?.correct_starting || ''} ${
                item?.correct_expanding || ''
              }`.trim();
            const allCorrectWords = correctSentenceToUse
              .split(' ')
              .filter((word) => word.length > 0);

            // Conditionally display student answer or correct answer
            const displayedStartWords =
              viewResult?.idx === index ? correctStarting : startTextWords;
            const displayedExpandWords =
              viewResult?.idx === index ? correctExpanding : expandTextWords;

            return (
              <motion.div
                key={index}
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.3, delay: 0.5 + index * 0.1 }}
                className={`rounded-lg p-4 border mb-4 ${
                  isCompleteSentenceCorrect
                    ? 'bg-green-50 border-green-200'
                    : 'bg-gray-50 border-gray-200'
                }`}
              >
                <div className="flex items-center gap-3">
                  <div className="flex items-center gap-2">
                    <span className="w-6 h-6 flex items-center justify-center text-sm font-bold">
                      {index + 1}.
                    </span>
                    {isCompleteSentenceCorrect && (
                      <Icon
                        icon="mdi:check-circle"
                        className="w-5 h-5 text-green-600"
                      />
                    )}
                  </div>
                  <div className="flex-1 flex items-center justify-between">
                    <div className="flex flex-wrap gap-1 items-center">
                      {/* Starting sentence words */}
                      {displayedStartWords.map((word, wordIndex) => {
                        // Check if the word exists in any of the correct complete sentences
                        const isCorrect = allCorrectWords.includes(word);

                        return (
                          <span
                            key={`start-${wordIndex}`}
                            className={`px-2 py-1 rounded text-sm font-medium ${
                              isCorrect
                                ? 'bg-blue-100 text-blue-800'
                                : 'bg-red-50 text-red-800 line-through'
                            }`}
                          >
                            {word}
                          </span>
                        );
                      })}

                      {/* Expanding sentence words */}
                      {displayedExpandWords.map((word, wordIndex) => {
                        // Check if the word exists in any of the correct complete sentences
                        const isCorrect = allCorrectWords.includes(word);

                        return (
                          <span
                            key={`expand-${wordIndex}`}
                            className={`px-2 py-1 rounded text-sm font-medium ${
                              isCorrect
                                ? 'bg-green-100 text-green-800'
                                : 'bg-red-100 text-red-800 line-through'
                            }`}
                          >
                            {word}
                          </span>
                        );
                      })}
                    </div>

                    {!isCompleteSentenceCorrect && (
                      <button
                        onClick={() =>
                          setVeiwResult((prev) =>
                            prev?.idx === index
                              ? null // Toggle back to student answer
                              : {
                                  idx: index,
                                  starting: correctStarting,
                                  expanding: correctExpanding,
                                }
                          )
                        }
                        className="px-4 py-2 min-w-[120px] rounded-lg bg-gradient-to-r from-yellow-200 to-yellow-300 hover:from-yellow-300 hover:to-yellow-400 text-yellow-800 text-[10px] font-semibold border border-yellow-400 shadow-md hover:shadow-lg transition-all duration-200 flex items-center gap-2"
                      >
                        <Icon
                          icon={
                            viewResult?.idx === index
                              ? 'mdi:eye-off'
                              : 'mdi:eye'
                          }
                          className="text-sm"
                        />
                        {viewResult?.idx === index
                          ? 'Hide Answer'
                          : 'View Answer'}
                      </button>
                    )}
                  </div>
                </div>
              </motion.div>
            );
          })}
        </div>
      </motion.div>

      {/* Action Buttons */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.6 }}
        className="flex flex-col sm:flex-row justify-center gap-3 sm:gap-4"
      >
        <motion.button
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}
          onClick={onRestart}
          className="bg-gradient-to-r from-yellow-500 to-yellow-600 hover:from-yellow-600 hover:to-yellow-700 text-white px-6 py-3 sm:px-8 sm:py-3 rounded-full font-medium shadow transition-all duration-200 flex items-center justify-center gap-2"
        >
          <Icon icon="mdi:refresh" className="w-4 h-4 sm:w-5 sm:h-5" />
          Play Again
        </motion.button>

        <Link
          href="/block"
          className="bg-gray-100 hover:bg-gray-200 text-gray-700 px-6 py-3 sm:px-8 sm:py-3 rounded-full font-medium transition-all duration-200 flex items-center justify-center gap-2"
        >
          <Icon icon="mdi:arrow-left" className="w-4 h-4 sm:w-5 sm:h-5" />
          Exit Game
        </Link>
      </motion.div>

      {/* Decorative Elements */}
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 1, delay: 0.8 }}
        className="mt-12 text-center"
      >
        <Image
          src="/assets/Frame 1000007108.png"
          alt="Decoration"
          width={500}
          height={20}
          className="w-full h-auto opacity-60"
        />
      </motion.div>
    </div>
  );
};

export default GameResultsStage;
