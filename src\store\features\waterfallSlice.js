import { createSlice } from '@reduxjs/toolkit';

const initialState = {
  // Game Progress State
  currentQuestionIndex: 0,
  score: 0,
  gameCompleted: false,
  isSubmitting: false,
  userAnswers: [],
  submitResult: null,
  error: null,

  // Question State
  blanks: [],
  options: [],
  showFeedback: false,
  isCorrect: false,
  hasChecked: false,

  // Drag & Drop State
  activeId: null,
  activeOption: null,

  // Modal State
  showTimeExpiredModal: false,
  showAnswerCheckModal: false,
  submissionInitiated: false,

  // Timer State
  timeLeft: 0,
  timerExpired: false,
  hasTimeLimit: false,

  // Game Data
  questions: [],
  setId: '',
  initialData: null,

  // UI State
  isLoading: false,
  shouldFetchNewData: false,
};

export const waterfallSlice = createSlice({
  name: 'waterfall',
  initialState,
  reducers: {
    // Game Progress Actions
    setCurrentQuestionIndex: (state, action) => {
      state.currentQuestionIndex = action.payload;
    },
    incrementQuestionIndex: (state) => {
      state.currentQuestionIndex += 1;
    },
    setScore: (state, action) => {
      state.score = action.payload;
    },
    incrementScore: (state, action) => {
      state.score += action.payload || 10;
    },
    setGameCompleted: (state, action) => {
      state.gameCompleted = action.payload;
    },
    setIsSubmitting: (state, action) => {
      state.isSubmitting = action.payload;
    },
    setUserAnswers: (state, action) => {
      state.userAnswers = action.payload;
    },
    addUserAnswer: (state, action) => {
      const existingIndex = state.userAnswers.findIndex(
        answer => answer.question_id === action.payload.question_id
      );
      if (existingIndex !== -1) {
        state.userAnswers[existingIndex] = action.payload;
      } else {
        state.userAnswers.push(action.payload);
      }
    },
    setSubmitResult: (state, action) => {
      state.submitResult = action.payload;
    },
    setError: (state, action) => {
      state.error = action.payload;
    },

    // Question State Actions
    setBlanks: (state, action) => {
      state.blanks = action.payload;
    },
    updateBlank: (state, action) => {
      const { index, value } = action.payload;
      if (state.blanks[index] !== undefined) {
        state.blanks[index] = value;
      }
    },
    resetBlank: (state, action) => {
      const index = action.payload;
      if (state.blanks[index] !== undefined) {
        state.blanks[index] = null;
      }
    },
    setOptions: (state, action) => {
      state.options = action.payload;
    },
    updateOptionUsed: (state, action) => {
      const { optionId, used } = action.payload;
      const option = state.options.find(opt => opt.id === optionId);
      if (option) {
        option.used = used;
      }
    },
    setShowFeedback: (state, action) => {
      state.showFeedback = action.payload;
    },
    setIsCorrect: (state, action) => {
      state.isCorrect = action.payload;
    },
    setHasChecked: (state, action) => {
      state.hasChecked = action.payload;
    },

    // Drag & Drop Actions
    setActiveId: (state, action) => {
      state.activeId = action.payload;
    },
    setActiveOption: (state, action) => {
      state.activeOption = action.payload;
    },

    // Modal Actions
    setShowTimeExpiredModal: (state, action) => {
      state.showTimeExpiredModal = action.payload;
    },
    setShowAnswerCheckModal: (state, action) => {
      state.showAnswerCheckModal = action.payload;
    },
    setSubmissionInitiated: (state, action) => {
      state.submissionInitiated = action.payload;
    },
    closeAllModals: (state) => {
      state.showTimeExpiredModal = false;
      state.showAnswerCheckModal = false;
    },

    // Timer Actions
    setTimeLeft: (state, action) => {
      state.timeLeft = action.payload;
    },
    setTimerExpired: (state, action) => {
      state.timerExpired = action.payload;
    },
    setHasTimeLimit: (state, action) => {
      state.hasTimeLimit = action.payload;
    },

    // Game Data Actions
    setInitialData: (state, action) => {
      state.initialData = action.payload;
      state.questions = action.payload?.questions || [];
      state.setId = action.payload?.id || '';
    },

    // UI Actions
    setIsLoading: (state, action) => {
      state.isLoading = action.payload;
    },
    setShouldFetchNewData: (state, action) => {
      state.shouldFetchNewData = action.payload;
    },

    // Reset Actions
    resetGameState: (state) => {
      state.currentQuestionIndex = 0;
      state.score = 0;
      state.gameCompleted = false;
      state.isSubmitting = false;
      state.userAnswers = [];
      state.submitResult = null;
      state.error = null;
      state.submissionInitiated = false;
    },
    resetQuestionState: (state) => {
      state.blanks = [];
      state.options = [];
      state.showFeedback = false;
      state.isCorrect = false;
      state.hasChecked = false;
      state.activeId = null;
      state.activeOption = null;
    },
    resetModalState: (state) => {
      state.showTimeExpiredModal = false;
      state.showAnswerCheckModal = false;
    },
    resetTimerState: (state) => {
      state.timeLeft = 0;
      state.timerExpired = false;
      state.hasTimeLimit = false;
    },
    resetAllState: (state) => {
      Object.assign(state, initialState);
    },

    // Complex Actions
    initializeQuestion: (state, action) => {
      const { question, blankCount, shuffledOptions } = action.payload;
      state.blanks = Array(blankCount).fill(null);
      state.options = shuffledOptions;
      state.showFeedback = false;
      state.isCorrect = false;
      state.hasChecked = false;
      state.activeId = null;
      state.activeOption = null;
      state.showTimeExpiredModal = false;
      state.showAnswerCheckModal = false;
    },


  },
});

export const {
  // Game Progress Actions
  setCurrentQuestionIndex,
  incrementQuestionIndex,
  setScore,
  incrementScore,
  setGameCompleted,
  setIsSubmitting,
  setUserAnswers,
  addUserAnswer,
  setSubmitResult,
  setError,

  // Question State Actions
  setBlanks,
  updateBlank,
  resetBlank,
  setOptions,
  updateOptionUsed,
  setShowFeedback,
  setIsCorrect,
  setHasChecked,

  // Drag & Drop Actions
  setActiveId,
  setActiveOption,

  // Modal Actions
  setShowTimeExpiredModal,
  setShowAnswerCheckModal,
  setSubmissionInitiated,
  closeAllModals,

  // Timer Actions
  setTimeLeft,
  setTimerExpired,
  setHasTimeLimit,

  // Game Data Actions
  setInitialData,

  // UI Actions
  setIsLoading,
  setShouldFetchNewData,

  // Reset Actions
  resetGameState,
  resetQuestionState,
  resetModalState,
  resetTimerState,
  resetAllState,

  // Complex Actions
  initializeQuestion,
} = waterfallSlice.actions;

// Selectors
export const selectCurrentQuestionIndex = (state) => state.waterfall.currentQuestionIndex;
export const selectScore = (state) => state.waterfall.score;
export const selectGameCompleted = (state) => state.waterfall.gameCompleted;
export const selectIsSubmitting = (state) => state.waterfall.isSubmitting;
export const selectUserAnswers = (state) => state.waterfall.userAnswers;
export const selectSubmitResult = (state) => state.waterfall.submitResult;
export const selectError = (state) => state.waterfall.error;

export const selectBlanks = (state) => state.waterfall.blanks;
export const selectOptions = (state) => state.waterfall.options;
export const selectShowFeedback = (state) => state.waterfall.showFeedback;
export const selectIsCorrect = (state) => state.waterfall.isCorrect;
export const selectHasChecked = (state) => state.waterfall.hasChecked;

export const selectActiveId = (state) => state.waterfall.activeId;
export const selectActiveOption = (state) => state.waterfall.activeOption;

export const selectShowTimeExpiredModal = (state) => state.waterfall.showTimeExpiredModal;
export const selectShowAnswerCheckModal = (state) => state.waterfall.showAnswerCheckModal;
export const selectSubmissionInitiated = (state) => state.waterfall.submissionInitiated;

export const selectTimeLeft = (state) => state.waterfall.timeLeft;
export const selectTimerExpired = (state) => state.waterfall.timerExpired;
export const selectHasTimeLimit = (state) => state.waterfall.hasTimeLimit;

export const selectQuestions = (state) => state.waterfall.questions;
export const selectSetId = (state) => state.waterfall.setId;
export const selectInitialData = (state) => state.waterfall.initialData;

export const selectIsLoading = (state) => state.waterfall.isLoading;
export const selectShouldFetchNewData = (state) => state.waterfall.shouldFetchNewData;

// Computed Selectors
export const selectCurrentQuestion = (state) => {
  const questions = selectQuestions(state);
  const index = selectCurrentQuestionIndex(state);
  return questions[index] || null;
};

export const selectIsLastQuestion = (state) => {
  const questions = selectQuestions(state);
  const index = selectCurrentQuestionIndex(state);
  return index === questions.length - 1;
};

export const selectCurrentTimeLimit = (state) => {
  const currentQuestion = selectCurrentQuestion(state);
  return currentQuestion?.time_limit_in_seconds;
};

export default waterfallSlice.reducer;
