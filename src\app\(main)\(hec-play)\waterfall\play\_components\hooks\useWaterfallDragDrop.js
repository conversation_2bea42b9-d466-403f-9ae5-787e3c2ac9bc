import { useDispatch, useSelector } from 'react-redux';
import { useS<PERSON><PERSON>, useSensor, PointerSensor, KeyboardSensor, rectIntersection } from '@dnd-kit/core';
import {
  setActiveId,
  setActiveOption,
  updateOptionUsed,
  selectActiveId,
  selectActiveOption,
  selectOptions,
  selectBlanks,
} from '@/store/features/waterfallSlice';
import { useWaterfallQuestion } from './useWaterfallQuestion';

export const useWaterfallDragDrop = () => {
  const dispatch = useDispatch();
  const { handleDropOption } = useWaterfallQuestion();
  
  const activeId = useSelector(selectActiveId);
  const activeOption = useSelector(selectActiveOption);
  const options = useSelector(selectOptions);
  const blanks = useSelector(selectBlanks);

  // Configure sensors for drag and drop - maximum flexibility
  const sensors = useSensors(
    useSensor(PointerSensor, {
      // No activation constraints for immediate response
      activationConstraint: undefined,
    }),
    useSensor(KeyboardSensor)
  );

  // Use rect intersection for more accurate collision detection
  const collisionDetection = rectIntersection;

  // Handle drag start
  const handleDragStart = (event) => {
    const { active } = event;
    dispatch(setActiveId(active.id));

    // Find the option being dragged
    const draggedOption = options.find(option => option.id === active.id);
    if (draggedOption) {
      dispatch(setActiveOption(draggedOption));
    }
  };

  // Handle drag end
  const handleDragEnd = (event) => {
    const { active, over } = event;

    dispatch(setActiveId(null));
    dispatch(setActiveOption(null));

    if (!over) return;

    // Check if dropping on a blank
    if (over.id.startsWith('blank-')) {
      const blankIndex = parseInt(over.id.replace('blank-', ''));
      const draggedOption = options.find(option => option.id === active.id);

      if (draggedOption && !draggedOption.used) {
        // Check if the blank is already filled
        if (blanks[blankIndex] !== null) {
          // Find the option that was previously in this blank and mark it as unused
          const previousValue = blanks[blankIndex];
          const previousOption = options.find(opt => opt.text === previousValue);
          if (previousOption) {
            // Mark the previous option as unused
            dispatch(updateOptionUsed({ optionId: previousOption.id, used: false }));
          }
        }

        handleDropOption(blankIndex, draggedOption.text, draggedOption.id);
      }
    }
    // Handle dropping back to options area (to unuse an option)
    else if (over.id === 'options-container' && active.id.startsWith('blank-')) {
      // This would be for dragging from blanks back to options
      // For now, we'll handle this through the reset functionality
    }
  };

  // No transform modifiers for completely free dragging

  return {
    // State
    activeId,
    activeOption,

    // Sensors and collision detection
    sensors,
    collisionDetection,

    // Event handlers
    handleDragStart,
    handleDragEnd,
  };
};
