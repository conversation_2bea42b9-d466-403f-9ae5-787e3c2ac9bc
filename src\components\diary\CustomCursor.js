'use client';
import React, { useState, useEffect } from 'react';
import { useSelector } from 'react-redux';
import { selectActiveTool, selectBrushSettings } from '@/store/features/diarySlice';

const CustomCursor = ({ containerRef }) => {
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });
  const [isVisible, setIsVisible] = useState(false);
  const activeTool = useSelector(selectActiveTool);
  const brushSettings = useSelector(selectBrushSettings);

  useEffect(() => {
    const container = containerRef.current;
    if (!container) return;

    const handleMouseMove = (e) => {
      const rect = container.getBoundingClientRect();
      const x = e.clientX - rect.left;
      const y = e.clientY - rect.top;
      
      setMousePosition({ x, y });
      setIsVisible(true);
    };

    const handleMouseEnter = () => {
      setIsVisible(true);
    };

    const handleMouseLeave = () => {
      setIsVisible(false);
    };

    container.addEventListener('mousemove', handleMouseMove);
    container.addEventListener('mouseenter', handleMouseEnter);
    container.addEventListener('mouseleave', handleMouseLeave);

    return () => {
      container.removeEventListener('mousemove', handleMouseMove);
      container.removeEventListener('mouseenter', handleMouseEnter);
      container.removeEventListener('mouseleave', handleMouseLeave);
    };
  }, [containerRef]);

  // Only show custom cursor for drawing tools
  const shouldShowCustomCursor = ['brush', 'pencil', 'highlighter', 'eraser', 'pen'].includes(activeTool);
  
  if (!shouldShowCustomCursor || !isVisible) {
    return null;
  }

  const size = brushSettings.size || 5;
  const cursorSize = Math.max(size, 8); // Minimum visible size

  // Different styles for different tools
  const getCursorStyle = () => {
    const baseStyle = {
      position: 'absolute',
      left: mousePosition.x - cursorSize / 2,
      top: mousePosition.y - cursorSize / 2,
      width: cursorSize,
      height: cursorSize,
      borderRadius: '50%',
      pointerEvents: 'none',
      zIndex: 1000,
      transform: 'translate(0, 0)', // Prevent sub-pixel rendering issues
    };

    switch (activeTool) {
      case 'brush':
        return {
          ...baseStyle,
          border: '2px solid #3B82F6',
          backgroundColor: 'rgba(59, 130, 246, 0.2)',
        };
      case 'pencil':
        return {
          ...baseStyle,
          border: '2px solid #6B7280',
          backgroundColor: 'rgba(107, 114, 128, 0.2)',
        };
      case 'highlighter':
        return {
          ...baseStyle,
          border: '2px solid #F59E0B',
          backgroundColor: 'rgba(245, 158, 11, 0.3)',
        };
      case 'pen':
        return {
          ...baseStyle,
          border: '2px solid #8B5CF6',
          backgroundColor: 'rgba(139, 92, 246, 0.2)',
        };
      case 'eraser':
        return {
          ...baseStyle,
          border: '2px solid #EF4444',
          backgroundColor: 'rgba(239, 68, 68, 0.2)',
          borderStyle: 'dashed',
        };
      default:
        return baseStyle;
    }
  };

  return (
    <div style={getCursorStyle()}>
      {/* Inner dot to show exact center */}
      <div
        style={{
          position: 'absolute',
          left: '50%',
          top: '50%',
          width: '2px',
          height: '2px',
          backgroundColor: activeTool === 'eraser' ? '#EF4444' : '#374151',
          borderRadius: '50%',
          transform: 'translate(-50%, -50%)',
        }}
      />
    </div>
  );
};

export default CustomCursor;