'use client';
import React, { useRef, useEffect, useState } from 'react';
import { useDispatch } from 'react-redux';
import Moveable from 'react-moveable';
import {
  updateDecorationItem,
  deleteDecorationItem,
  saveDecorationHistory,
} from '@/store/features/diarySlice';
import Shape<PERSON>enderer from './ShapeRenderer';
import ImageRenderer from './ImageRenderer';
import { isMobileDevice, getOptimalTouchTargetSize } from '@/lib/mobileUtils';

const DecorationWrapper = ({
  id,
  item,
  selectedId,
  onSelect,
  activeTool,
  isDecorating = true,
  scale = 1,
  isMobile: propIsMobile,
  isTouch: propIsTouch,
}) => {
  const elementRef = useRef(null);
  const moveableRef = useRef(null);
  const dispatch = useDispatch();
  const [isDragging, setIsDragging] = useState(false);
  const [isMobile, setIsMobile] = useState(propIsMobile || false);

  useEffect(() => {
    setIsMobile(propIsMobile !== undefined ? propIsMobile : isMobileDevice());
  }, [propIsMobile]);

  // Apply scale to position and size for display
  const [position, setPosition] = useState({
    x: item.x * scale,
    y: item.y * scale,
  });
  const [size, setSize] = useState({
    width: item.width * scale,
    height: item.height * scale,
  });
  const isSelected = selectedId === id;

  useEffect(() => {
    if (moveableRef.current) {
      moveableRef.current.updateRect();
    }
  }, [position, size]);

  // Update position and size when scale changes
  useEffect(() => {
    if (!isDragging) {
      setPosition({ x: item.x * scale, y: item.y * scale });
      setSize({ width: item.width * scale, height: item.height * scale });
    }
  }, [item.x, item.y, item.width, item.height, scale, isDragging]);

  const debounce = (func, wait) => {
    let timeout;
    return function executedFunction(...args) {
      const later = () => {
        clearTimeout(timeout);
        func(...args);
      };
      clearTimeout(timeout);
      timeout = setTimeout(later, wait);
    };
  };

  const updateReduxStore = debounce((newPosition, newSize) => {
    // Convert scaled values back to original scale for Redux storage
    const updateData = {
      id,
      updates: {
        x: newPosition.x / scale,
        y: newPosition.y / scale,
        width: newSize.width / scale,
        height: newSize.height / scale,
      },
    };
    console.log('Decoration Update:', updateData);
    dispatch(updateDecorationItem(updateData));
  }, 50); // Reduced debounce time for smoother dragging

  const handleDragStart = () => {
    setIsDragging(true);
    // Save current state to history before starting drag operation
    dispatch(saveDecorationHistory());
  };

  const handleDrag = ({ target, beforeTranslate }) => {
    const [x, y] = beforeTranslate;
    target.style.transform = `translate(${x}px, ${y}px)`;
    const newPosition = { x, y };
    setPosition(newPosition);
    // Only update Redux store when decorating (editing mode)
    if (isDecorating) {
      updateReduxStore(newPosition, size);
    }
  };

  const handleDragEnd = () => {
    setIsDragging(false);
  };

  const handleResizeStart = () => {
    // Save current state to history before starting resize operation
    dispatch(saveDecorationHistory());
  };

  const handleResize = ({ target, width, height, drag }) => {
    const { beforeTranslate } = drag;
    const [x, y] = beforeTranslate;
    target.style.width = `${width}px`;
    target.style.height = `${height}px`;
    target.style.transform = `translate(${x}px, ${y}px)`;

    const newPosition = { x, y };
    const newSize = { width, height };

    setPosition(newPosition);
    setSize(newSize);
    // Only update Redux store when decorating (editing mode)
    if (isDecorating) {
      updateReduxStore(newPosition, newSize);
    }
  };

  const handleClick = (e) => {
    e.stopPropagation();
    // Only allow selection when decorating
    if (isDecorating) {
      onSelect(id);
    }
  };

  const handleDelete = (e) => {
    e.stopPropagation();
    console.log('Decoration Delete:', { id, item });
    dispatch(deleteDecorationItem(id));
  };

  const canMove =
    activeTool === 'decoration' || activeTool === 'shapes' || !activeTool;

  const renderContent = () => {
    if (item.type === 'shape') {
      return <ShapeRenderer item={item} size={size} />;
    }
    if (item.type === 'decoration') {
      return <ImageRenderer item={item} />;
    }
    // For 'drawing' type, we don't render anything here, as it's on the canvas
    return null;
  };

  // Don't render a wrapper for drawing items, as they are not interactive elements
  if (item.type === 'drawing') {
    return null;
  }

  return (
    <>
      <div
        ref={elementRef}
        onClick={handleClick}
        className={`absolute decoration-wrapper decoration-element ${
          isDecorating ? 'cursor-pointer' : 'cursor-default'
        } ${isMobile ? 'mobile-optimized' : ''}`}
        style={{
          width: size.width,
          height: size.height,
          transform: `translate(${position.x}px, ${position.y}px)`,
          border: isSelected ? '2px solid #3b82f6' : 'none',
          zIndex: item.zIndex || 10,
          minWidth: isMobile ? '44px' : 'auto',
          minHeight: isMobile ? '44px' : 'auto',
        }}
      >
        {renderContent()}

        {isSelected && (
          <>
            <button
              onClick={handleDelete}
              className={`absolute -top-6 -right-6 bg-red-500 text-white rounded-full text-xs flex items-center justify-center hover:bg-red-600 transition-colors z-50 touch-manipulation decoration-delete-btn ${
                isMobile ? 'w-8 h-8' : 'w-6 h-6'
              }`}
              title="Delete decoration"
              style={{
                minWidth: `${getOptimalTouchTargetSize()}px`,
                minHeight: `${getOptimalTouchTargetSize()}px`,
                fontSize: isMobile ? '16px' : '12px',
              }}
            >
              ×
            </button>

            {item.title && (
              <div className="max-sm:hidden absolute -top-8 left-0 bg-black bg-opacity-75 text-white text-xs px-2 py-1 rounded whitespace-nowrap pointer-events-none z-40">
                {item.title}
              </div>
            )}
          </>
        )}
      </div>

      {isSelected && elementRef.current && canMove && isDecorating && (
        <Moveable
          ref={moveableRef}
          target={elementRef.current}
          container={null}
          origin={false}
          draggable={true}
          resizable={true}
          onDragStart={handleDragStart}
          onDrag={handleDrag}
          onDragEnd={handleDragEnd}
          onResizeStart={handleResizeStart}
          onResize={handleResize}
          snappable
          throttleDrag={1}
          throttleResize={1}
          renderDirections={['nw', 'n', 'ne', 'w', 'e', 'sw', 's', 'se']}
          edge={false}
          zoom={1}
          scalable={false}
          rotatable={false}
          warpable={false}
          pinchable={false}
          keepRatio={false}
        />
      )}
    </>
  );
};

export default DecorationWrapper;
